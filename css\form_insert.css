@import url('https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
*{
    margin: 0;
    padding:0;
    box-sizing: border-box;
    font-family:'Prompt', sans-serif;
}
body{
    background: linear-gradient(120deg, #2980b9, #8e44ad);
    margin: 0;
    line-height: 1.5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
}

.container{
    background: #fff;
    width: 50%;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 0px 2.2px rgba(0, 0, 0, 0.028), 0 0px 5.3px rgba(0, 0, 0, 0.04), 0 0px 10px rgba(0, 0, 0, 0.05);
}

h2{
    text-align: center;
    padding: 5px;
}

.form-grop label,
.form-grop input,
.form-grop select,
.form-grop textarea{
    display: block;
    width: 100%;
    padding: 10px 10px 10px 5px;
    font-size: 16px;
    outline: none;
}

.form-grop input[type="text"],
.form-grop select,
.form-grop textarea{
    border: 1px solid #78a8c9;
    border-radius: 3px;
}


.form-grop input[type="file"]{
    width: 20vw;
}

.form-grop label{
    font-weight: 600;
    font-size: 18px;
    margin: 10px 0 2px 0;
}

button{
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%);
    width: 200px;
    height: 40px;
    background: #2691d9;
    border: none;
    outline: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1em;
    color: #e9f4fb;
    font-weight: 700;
    margin-top: 30px;
    margin-bottom: 20px;
}