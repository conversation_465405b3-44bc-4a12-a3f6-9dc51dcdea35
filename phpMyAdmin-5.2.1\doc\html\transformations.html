
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Transformations &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="Bookmarks" href="bookmarks.html" />
    <link rel="prev" title="Two-factor authentication" href="two_factor.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="bookmarks.html" title="Bookmarks"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" accesskey="U">User Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Transformations</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="transformations">
<span id="id1"></span><h1>Transformations<a class="headerlink" href="#transformations" title="Permalink to this headline">¶</a></h1>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You need to have configured the <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a> to use the transformations
feature.</p>
</div>
<div class="section" id="introduction">
<span id="transformationsintro"></span><h2>Introduction<a class="headerlink" href="#introduction" title="Permalink to this headline">¶</a></h2>
<p>To enable transformations, you have to set up the <code class="docutils literal notranslate"><span class="pre">column_info</span></code>
table and the proper directives. Please see the <a class="reference internal" href="config.html#config"><span class="std std-ref">Configuration</span></a> on how to do so.</p>
<p>phpMyAdmin has two different types of transformations: browser display
transformations, which affect only how the data is shown when browsing
through phpMyAdmin; and input transformations, which affect a value
prior to being inserted through phpMyAdmin.
You can apply different transformations to the contents of each
column. Each transformation has options to define how it will affect the
stored data.</p>
<p>Say you have a column <code class="docutils literal notranslate"><span class="pre">filename</span></code> which contains a filename. Normally
you would see in phpMyAdmin only this filename. Using display transformations
you can transform that filename into a HTML link, so you can click
inside of the phpMyAdmin structure on the column’s link and will see
the file displayed in a new browser window. Using transformation
options you can also specify strings to append/prepend to a string or
the format you want the output stored in.</p>
<p>For a general overview of all available transformations and their
options, you can either go to the <code class="docutils literal notranslate"><span class="pre">Change</span></code> link for an existing column
or from the dialog to create a new column, in either case there is a link
on that column structure page for “Browser display transformation” and
“Input transformation” which will show more information about each
transformation that is available on your system.</p>
<p>For a tutorial on how to effectively use transformations, see our
<a class="reference external" href="https://www.phpmyadmin.net/docs/">Link section</a> on the
official phpMyAdmin homepage.</p>
</div>
<div class="section" id="usage">
<span id="transformationshowto"></span><h2>Usage<a class="headerlink" href="#usage" title="Permalink to this headline">¶</a></h2>
<p>Go to the table structure page (reached by clicking on
the ‘Structure’ link for a table). There click on “Change” (or the change
icon) and there you will see the five transformation–related fields at the end of the line.
They are called ‘<a class="reference internal" href="glossary.html#term-Media-type"><span class="xref std std-term">Media type</span></a>’, ‘Browser transformation’ and
‘Transformation options’.</p>
<ul class="simple">
<li><p>The field ‘<a class="reference internal" href="glossary.html#term-Media-type"><span class="xref std std-term">Media type</span></a>’ is a drop-down field. Select the <a class="reference internal" href="glossary.html#term-Media-type"><span class="xref std std-term">Media type</span></a> that
corresponds to the column’s contents. Please note that many transformations
are inactive until a <a class="reference internal" href="glossary.html#term-Media-type"><span class="xref std std-term">Media type</span></a> is selected.</p></li>
<li><p>The field ‘Browser display transformation’ is a drop-down field. You can
choose from a hopefully growing amount of pre-defined transformations.
See below for information on how to build your own transformation.
There are global transformations and mimetype-bound transformations.
Global transformations can be used for any mimetype. They will take
the mimetype, if necessary, into regard. Mimetype-bound
transformations usually only operate on a certain mimetype. There are
transformations which operate on the main mimetype (like ‘image’),
which will most likely take the subtype into regard, and those who
only operate on a specific subtype (like ‘image/jpeg’). You can use
transformations on mimetypes for which the function was not defined
for. There is no security check for you selected the right
transformation, so take care of what the output will be like.</p></li>
<li><p>The field ‘Browser display transformation options’ is a free-type textfield. You have
to enter transform-function specific options here. Usually the
transforms can operate with default options, but it is generally a
good idea to look up the overview to see which options are necessary.
Much like the ENUM/SET-Fields, you have to split up several options
using the format ‘a’,’b’,’c’,…(NOTE THE MISSING BLANKS). This is
because internally the options will be parsed as an array, leaving the
first value the first element in the array, and so forth. If you want
to specify a MIME character set you can define it in the
transformation_options. You have to put that outside of the pre-
defined options of the specific mime-transform, as the last value of
the set. Use the format “’; charset=XXX’”. If you use a transform, for
which you can specify 2 options and you want to append a character
set, enter “‘first parameter’,’second parameter’,’charset=us-ascii’”.
You can, however use the defaults for the parameters: “’’,’’,’charset
=us-ascii’”. The default options can be configured using
<span class="target" id="index-0"></span><a class="reference internal" href="config.html#cfg_DefaultTransformations"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['DefaultTransformations']</span></code></a>.</p></li>
<li><p>‘Input transformation’ is another drop-down menu that corresponds exactly
with the instructions above for “Browser display transformation” except
these these affect the data before insertion in to the database. These are
most commonly used to either provide a specialized editor (for example, using
the phpMyAdmin SQL editor interface) or selector (such as for uploading an image).
It’s also possible to manipulate the data such as converting an IPv4 address to binary
or parsing it through a regular expression.</p></li>
<li><p>Finally, ‘Input transformation options’ is the equivalent of the “Browser display
transformation options” section above and is where optional and required parameters are entered.</p></li>
</ul>
</div>
<div class="section" id="file-structure">
<span id="transformationsfiles"></span><h2>File structure<a class="headerlink" href="#file-structure" title="Permalink to this headline">¶</a></h2>
<p>All specific transformations for mimetypes are defined through class
files in the directory <code class="file docutils literal notranslate"><span class="pre">libraries/classes/Plugins/Transformations/</span></code>. Each of
them extends a certain transformation abstract class declared in
<code class="file docutils literal notranslate"><span class="pre">libraries/classes/Plugins/Transformations/Abs</span></code>.</p>
<p>They are stored in files to ease customization and to allow easy adding of
new or custom transformations.</p>
<p>Because the user cannot enter their own mimetypes, it is kept certain that
the transformations will always work. It makes no sense to apply a
transformation to a mimetype the transform-function doesn’t know to
handle.</p>
<p>There is a file called <code class="file docutils literal notranslate"><span class="pre">libraries/classes/Plugins/Transformations.php</span></code> that provides some
basic functions which can be included by any other transform function.</p>
<p>The file name convention is <code class="docutils literal notranslate"><span class="pre">[Mimetype]_[Subtype]_[Transformation</span>
<span class="pre">Name].php</span></code>, while the abstract class that it extends has the
name <code class="docutils literal notranslate"><span class="pre">[Transformation</span> <span class="pre">Name]TransformationsPlugin</span></code>. All of the
methods that have to be implemented by a transformations plug-in are:</p>
<ol class="arabic simple">
<li><p>getMIMEType() and getMIMESubtype() in the main class;</p></li>
<li><p>getName(), getInfo() and applyTransformation() in the abstract class
it extends.</p></li>
</ol>
<p>The getMIMEType(), getMIMESubtype() and getName() methods return the
name of the MIME type, MIME Subtype and transformation accordingly.
getInfo() returns the transformation’s description and possible
options it may receive and applyTransformation() is the method that
does the actual work of the transformation plug-in.</p>
<p>Please see the <code class="file docutils literal notranslate"><span class="pre">libraries/classes/Plugins/Transformations/TEMPLATE</span></code> and
<code class="file docutils literal notranslate"><span class="pre">libraries/classes/Plugins/Transformations/TEMPLATE_ABSTRACT</span></code> files for adding
your own transformation plug-in. You can also generate a new
transformation plug-in (with or without the abstract transformation
class), by using
<code class="file docutils literal notranslate"><span class="pre">scripts/transformations_generator_plugin.sh</span></code> or
<code class="file docutils literal notranslate"><span class="pre">scripts/transformations_generator_main_class.sh</span></code>.</p>
<p>The applyTransformation() method always gets passed three variables:</p>
<ol class="arabic simple">
<li><p><strong>$buffer</strong> - Contains the text inside of the column. This is the
text, you want to transform.</p></li>
<li><p><strong>$options</strong> - Contains any user-passed options to a transform
function as an array.</p></li>
<li><p><strong>$meta</strong> - Contains an object with information about your column. The
data is drawn from the output of the <a class="reference external" href="https://www.php.net/mysql_fetch_field">mysql_fetch_field()</a> function. This means, all
object properties described on the <a class="reference external" href="https://www.php.net/mysql_fetch_field">manual page</a> are available in this
variable and can be used to transform a column accordingly to
unsigned/zerofill/not_null/… properties. The $meta-&gt;mimetype
variable contains the original <a class="reference internal" href="glossary.html#term-Media-type"><span class="xref std std-term">Media type</span></a> of the column (i.e.
‘text/plain’, ‘image/jpeg’ etc.)</p></li>
</ol>
</div>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Transformations</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#usage">Usage</a></li>
<li><a class="reference internal" href="#file-structure">File structure</a></li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="two_factor.html"
                        title="previous chapter">Two-factor authentication</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="bookmarks.html"
                        title="next chapter">Bookmarks</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/transformations.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="bookmarks.html" title="Bookmarks"
             >next</a> |</li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" >User Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Transformations</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>