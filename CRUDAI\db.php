<?php
$serverName = "localhost"; // Replace with your server name or IP
$database = "quiz_db"; // Your database name
$username = "<PERSON><PERSON><PERSON>"; // SQL Server username
$password = "<PERSON>@03032531"; // SQL Server password

try {
    $conn = new PDO("sqlsrv:Server=$serverName;Database=$database", $username, $password);
    // Set error mode to exception
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Connected successfully"; // Remove this after testing
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>
