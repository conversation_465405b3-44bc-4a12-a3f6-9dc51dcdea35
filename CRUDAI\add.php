
<?php include 'db.php'; ?>

<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]>      <html class="no-js"> <!--<![endif]-->
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>Add pdf</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="tailwind.css">
    </head>
    <body class="bg-gray-100 p-10">
        <div class="max-w-lg mx-auto bg-white p-6 rounded shadow">
            <h1 class="text-xl font-bold mb-4">Upload PDF</h1>
            <form action="add.php" method="POST" enctype="multipart/form-data">
                <label class="block mb-2">Select PDF:</label>
                <input type="file" name="pdf_file" accept="application/pdf" class="block w-full border p-2 mb-4">
                <button type="submit" name="submit" class="bg-blue-500 text-white py-2 px-4 rounded">Upload</button>
            </form>
        </div>

        <?php
        if (isset($_POST['submit'])) {
            $file_name = $_FILES['pdf_file']['name'];
            $file_tmp = $_FILES['pdf_file']['tmp_name'];

            if (!empty($file_name)) {
                $target_dir = "upload/" . $file_name;
                move_uploaded_file($file_tmp, $target_dir);

                $stmt = $conn->prepare("INSERT INTO pdf_files (file_name) VALUES (?)");
                $stmt->bind_param("s", $file_name);
                $stmt->execute();
                echo "<p class='text-green-500 mt-4'>File uploaded successfully!</p>";
            } else {
                echo "<p class='text-red-500 mt-4'>Please select a file.</p>";
            }
        }
        ?>
    </body>
</html>