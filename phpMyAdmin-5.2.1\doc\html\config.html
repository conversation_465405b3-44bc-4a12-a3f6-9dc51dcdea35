
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Configuration &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="User Guide" href="user.html" />
    <link rel="prev" title="Installation" href="setup.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="user.html" title="User Guide"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="setup.html" title="Installation"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Configuration</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="configuration">
<span id="config"></span><span id="index-0"></span><h1>Configuration<a class="headerlink" href="#configuration" title="Permalink to this headline">¶</a></h1>
<p>All configurable data is placed in <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> in phpMyAdmin’s
toplevel directory.  If this file does not exist, please refer to the
<a class="reference internal" href="setup.html#setup"><span class="std std-ref">Installation</span></a> section to create one. This file only needs to contain the
parameters you want to change from their corresponding default value in
<code class="file docutils literal notranslate"><span class="pre">libraries/config.default.php</span></code> (this file is not intended for changes).</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#config-examples"><span class="std std-ref">Examples</span></a> for examples of configurations</p>
</div>
<p>If a directive is missing from your file, you can just add another line with
the file. This file is for over-writing the defaults; if you wish to use the
default value there’s no need to add a line here.</p>
<p>The parameters which relate to design (like colors) are placed in
<code class="file docutils literal notranslate"><span class="pre">themes/themename/scss/_variables.scss</span></code>. You might also want to create
<code class="file docutils literal notranslate"><span class="pre">config.footer.inc.php</span></code> and <code class="file docutils literal notranslate"><span class="pre">config.header.inc.php</span></code> files to add
your site specific code to be included on start and end of each page.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Some distributions (eg. Debian or Ubuntu) store <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> in
<code class="docutils literal notranslate"><span class="pre">/etc/phpmyadmin</span></code> instead of within phpMyAdmin sources.</p>
</div>
<div class="section" id="basic-settings">
<h2>Basic settings<a class="headerlink" href="#basic-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_PmaAbsoluteUri">
<code class="sig-name descname">$cfg['PmaAbsoluteUri']</code><a class="headerlink" href="#cfg_PmaAbsoluteUri" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 4.6.5: </span>This setting was not available in phpMyAdmin 4.6.0 - 4.6.4.</p>
</div>
<p>Sets here the complete <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> (with full path) to your phpMyAdmin
installation’s directory. E.g.
<code class="docutils literal notranslate"><span class="pre">https://www.example.net/path_to_your_phpMyAdmin_directory/</span></code>. Note also
that the <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> on most of web servers are case sensitive (even on
Windows). Don’t forget the trailing slash at the end.</p>
<p>Starting with version 2.3.0, it is advisable to try leaving this blank. In
most cases phpMyAdmin automatically detects the proper setting. Users of
port forwarding or complex reverse proxy setup might need to set this.</p>
<p>A good test is to browse a table, edit a row and save it. There should be
an error message if phpMyAdmin is having trouble auto–detecting the correct
value. If you get an error that this must be set or if the autodetect code
fails to detect your path, please post a bug report on our bug tracker so
we can improve the code.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faq1-40"><span class="std std-ref">1.40 When accessing phpMyAdmin via an Apache reverse proxy, cookie login does not work.</span></a>, <a class="reference internal" href="faq.html#faq2-5"><span class="std std-ref">2.5 Each time I want to insert or change a row or drop a database or a table, an error 404 (page not found) is displayed or, with HTTP or cookie authentication, I’m asked to log in again. What’s wrong?</span></a>, <a class="reference internal" href="faq.html#faq4-7"><span class="std std-ref">4.7 Authentication window is displayed more than once, why?</span></a>, <a class="reference internal" href="faq.html#faq5-16"><span class="std std-ref">5.16 With Internet Explorer, I get “Access is denied” Javascript errors. Or I cannot make phpMyAdmin work under Windows.</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_PmaNoRelation_DisableWarning">
<code class="sig-name descname">$cfg['PmaNoRelation_DisableWarning']</code><a class="headerlink" href="#cfg_PmaNoRelation_DisableWarning" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Starting with version 2.3.0 phpMyAdmin offers a lot of features to
work with master / foreign – tables (see <span class="target" id="index-1"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a>).</p>
<p>If you tried to set this
up and it does not work for you, have a look on the <span class="guilabel">Structure</span> page
of one database where you would like to use it. You will find a link
that will analyze why those features have been disabled.</p>
<p>If you do not want to use those features set this variable to <code class="docutils literal notranslate"><span class="pre">true</span></code> to
stop this message from appearing.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_AuthLog">
<code class="sig-name descname">$cfg['AuthLog']</code><a class="headerlink" href="#cfg_AuthLog" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'auto'</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.8.0: </span>This is supported since phpMyAdmin 4.8.0.</p>
</div>
<p>Configure authentication logging destination. Failed (or all, depending on
<span class="target" id="index-2"></span><a class="reference internal" href="#cfg_AuthLogSuccess"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['AuthLogSuccess']</span></code></a>) authentication attempts will be
logged according to this directive:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">auto</span></code></dt><dd><p>Let phpMyAdmin automatically choose between <code class="docutils literal notranslate"><span class="pre">syslog</span></code> and <code class="docutils literal notranslate"><span class="pre">php</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">syslog</span></code></dt><dd><p>Log using syslog, using AUTH facility, on most systems this ends up
in <code class="file docutils literal notranslate"><span class="pre">/var/log/auth.log</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">php</span></code></dt><dd><p>Log into PHP error log.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">sapi</span></code></dt><dd><p>Log into PHP SAPI logging.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">/path/to/file</span></code></dt><dd><p>Any other value is treated as a filename and log entries are written there.</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When logging to a file, make sure its permissions are correctly set
for a web server user, the setup should closely match instructions
described in <span class="target" id="index-3"></span><a class="reference internal" href="#cfg_TempDir"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['TempDir']</span></code></a>:</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_AuthLogSuccess">
<code class="sig-name descname">$cfg['AuthLogSuccess']</code><a class="headerlink" href="#cfg_AuthLogSuccess" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.8.0: </span>This is supported since phpMyAdmin 4.8.0.</p>
</div>
<p>Whether to log successful authentication attempts into
<span class="target" id="index-4"></span><a class="reference internal" href="#cfg_AuthLog"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['AuthLog']</span></code></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SuhosinDisableWarning">
<code class="sig-name descname">$cfg['SuhosinDisableWarning']</code><a class="headerlink" href="#cfg_SuhosinDisableWarning" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>A warning is displayed on the main page if Suhosin is detected.</p>
<p>You can set this parameter to <code class="docutils literal notranslate"><span class="pre">true</span></code> to stop this message from appearing.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_LoginCookieValidityDisableWarning">
<code class="sig-name descname">$cfg['LoginCookieValidityDisableWarning']</code><a class="headerlink" href="#cfg_LoginCookieValidityDisableWarning" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>A warning is displayed on the main page if the PHP parameter
session.gc_maxlifetime is lower than cookie validity configured in phpMyAdmin.</p>
<p>You can set this parameter to <code class="docutils literal notranslate"><span class="pre">true</span></code> to stop this message from appearing.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ServerLibraryDifference_DisableWarning">
<code class="sig-name descname">$cfg['ServerLibraryDifference_DisableWarning']</code><a class="headerlink" href="#cfg_ServerLibraryDifference_DisableWarning" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.7.0: </span>This setting was removed as the warning has been removed as well.</p>
</div>
<p>A warning is displayed on the main page if there is a difference
between the MySQL library and server version.</p>
<p>You can set this parameter to <code class="docutils literal notranslate"><span class="pre">true</span></code> to stop this message from appearing.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ReservedWordDisableWarning">
<code class="sig-name descname">$cfg['ReservedWordDisableWarning']</code><a class="headerlink" href="#cfg_ReservedWordDisableWarning" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>This warning is displayed on the Structure page of a table if one or more
column names match with words which are MySQL reserved.</p>
<p>If you want to turn off this warning, you can set it to <code class="docutils literal notranslate"><span class="pre">true</span></code> and
warning will no longer be displayed.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_TranslationWarningThreshold">
<code class="sig-name descname">$cfg['TranslationWarningThreshold']</code><a class="headerlink" href="#cfg_TranslationWarningThreshold" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>80</p>
</dd>
</dl>
<p>Show warning about incomplete translations on certain threshold.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SendErrorReports">
<code class="sig-name descname">$cfg['SendErrorReports']</code><a class="headerlink" href="#cfg_SendErrorReports" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'ask'</span></code></p>
</dd>
</dl>
<p>Valid values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">ask</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">always</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">never</span></code></p></li>
</ul>
<p>Sets the default behavior for JavaScript error reporting.</p>
<p>Whenever an error is detected in the JavaScript execution, an error report
may be sent to the phpMyAdmin team if the user agrees.</p>
<p>The default setting of <code class="docutils literal notranslate"><span class="pre">'ask'</span></code> will ask the user everytime there is a new
error report. However you can set this parameter to <code class="docutils literal notranslate"><span class="pre">'always'</span></code> to send error
reports without asking for confirmation or you can set it to <code class="docutils literal notranslate"><span class="pre">'never'</span></code> to
never send error reports.</p>
<p>This directive is available both in the configuration file and in users
preferences. If the person in charge of a multi-user installation prefers
to disable this feature for all users, a value of <code class="docutils literal notranslate"><span class="pre">'never'</span></code> should be
set, and the <span class="target" id="index-5"></span><a class="reference internal" href="#cfg_UserprefsDisallow"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['UserprefsDisallow']</span></code></a> directive should
contain <code class="docutils literal notranslate"><span class="pre">'SendErrorReports'</span></code> in one of its array values.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ConsoleEnterExecutes">
<code class="sig-name descname">$cfg['ConsoleEnterExecutes']</code><a class="headerlink" href="#cfg_ConsoleEnterExecutes" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Setting this to <code class="docutils literal notranslate"><span class="pre">true</span></code> allows the user to execute queries by pressing Enter
instead of Ctrl+Enter. A new line can be inserted by pressing Shift+Enter.</p>
<p>The behaviour of the console can be temporarily changed using console’s
settings interface.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_AllowThirdPartyFraming">
<code class="sig-name descname">$cfg['AllowThirdPartyFraming']</code><a class="headerlink" href="#cfg_AllowThirdPartyFraming" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean|string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Setting this to <code class="docutils literal notranslate"><span class="pre">true</span></code> allows phpMyAdmin to be included inside a frame,
and is a potential security hole allowing cross-frame scripting attacks or
clickjacking. Setting this to ‘sameorigin’ prevents phpMyAdmin to be
included from another document in a frame, unless that document belongs
to the same domain.</p>
</dd></dl>

</div>
<div class="section" id="server-connection-settings">
<h2>Server connection settings<a class="headerlink" href="#server-connection-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_Servers">
<code class="sig-name descname">$cfg['Servers']</code><a class="headerlink" href="#cfg_Servers" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>one server array with settings listed below</p>
</dd>
</dl>
<p>Since version 1.4.2, phpMyAdmin supports the administration of multiple
MySQL servers. Therefore, a <span class="target" id="index-6"></span><a class="reference internal" href="#cfg_Servers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers']</span></code></a>-array has been
added which contains the login information for the different servers. The
first <span class="target" id="index-7"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a> contains the hostname of
the first server, the second <span class="target" id="index-8"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a>
the hostname of the second server, etc. In
<code class="file docutils literal notranslate"><span class="pre">libraries/config.default.php</span></code>, there is only one section for server
definition, however you can put as many as you need in
<code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code>, copy that block or needed parts (you don’t have to
define all settings, just those you need to change).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <span class="target" id="index-9"></span><a class="reference internal" href="#cfg_Servers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers']</span></code></a> array starts with
$cfg[‘Servers’][1]. Do not use $cfg[‘Servers’][0]. If you want more
than one server, just copy following section (including $i
increment) several times. There is no need to define full server
array, just define values you need to change.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_host">
<code class="sig-name descname">$cfg['Servers'][$i]['host']</code><a class="headerlink" href="#cfg_Servers_host" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'localhost'</span></code></p>
</dd>
</dl>
<p>The hostname or <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> address of your $i-th MySQL-server. E.g.
<code class="docutils literal notranslate"><span class="pre">localhost</span></code>.</p>
<p>Possible values are:</p>
<ul class="simple">
<li><p>hostname, e.g., <code class="docutils literal notranslate"><span class="pre">'localhost'</span></code> or <code class="docutils literal notranslate"><span class="pre">'mydb.example.org'</span></code></p></li>
<li><p>IP address, e.g., <code class="docutils literal notranslate"><span class="pre">'127.0.0.1'</span></code> or <code class="docutils literal notranslate"><span class="pre">'************'</span></code></p></li>
<li><p>IPv6 address, e.g. <code class="docutils literal notranslate"><span class="pre">2001:cdba:0000:0000:0000:0000:3257:9652</span></code></p></li>
<li><p>dot - <code class="docutils literal notranslate"><span class="pre">'.'</span></code>, i.e., use named pipes on windows systems</p></li>
<li><p>empty - <code class="docutils literal notranslate"><span class="pre">''</span></code>, disables this server</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The hostname <code class="docutils literal notranslate"><span class="pre">localhost</span></code> is handled specially by MySQL and it uses
the socket based connection protocol. To use TCP/IP networking, use an
IP address or hostname such as <code class="docutils literal notranslate"><span class="pre">127.0.0.1</span></code> or <code class="docutils literal notranslate"><span class="pre">db.example.com</span></code>. You
can configure the path to the socket with
<span class="target" id="index-10"></span><a class="reference internal" href="#cfg_Servers_socket"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['socket']</span></code></a>.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-11"></span><a class="reference internal" href="#cfg_Servers_port"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['port']</span></code></a>,
&lt;<a class="reference external" href="https://dev.mysql.com/doc/refman/8.0/en/connecting.html">https://dev.mysql.com/doc/refman/8.0/en/connecting.html</a>&gt;</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_port">
<code class="sig-name descname">$cfg['Servers'][$i]['port']</code><a class="headerlink" href="#cfg_Servers_port" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The port-number of your $i-th MySQL-server. Default is 3306 (leave
blank).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you use <code class="docutils literal notranslate"><span class="pre">localhost</span></code> as the hostname, MySQL ignores this port number
and connects with the socket, so if you want to connect to a port
different from the default port, use <code class="docutils literal notranslate"><span class="pre">127.0.0.1</span></code> or the real hostname
in <span class="target" id="index-12"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a>.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-13"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a>,
&lt;<a class="reference external" href="https://dev.mysql.com/doc/refman/8.0/en/connecting.html">https://dev.mysql.com/doc/refman/8.0/en/connecting.html</a>&gt;</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_socket">
<code class="sig-name descname">$cfg['Servers'][$i]['socket']</code><a class="headerlink" href="#cfg_Servers_socket" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The path to the socket to use. Leave blank for default. To determine
the correct socket, check your MySQL configuration or, using the
<strong class="command">mysql</strong> command–line client, issue the <code class="docutils literal notranslate"><span class="pre">status</span></code> command. Among the
resulting information displayed will be the socket used.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This takes effect only if <span class="target" id="index-14"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a> is set
to <code class="docutils literal notranslate"><span class="pre">localhost</span></code>.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-15"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a>,
&lt;<a class="reference external" href="https://dev.mysql.com/doc/refman/8.0/en/connecting.html">https://dev.mysql.com/doc/refman/8.0/en/connecting.html</a>&gt;</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_ssl">
<code class="sig-name descname">$cfg['Servers'][$i]['ssl']</code><a class="headerlink" href="#cfg_Servers_ssl" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Whether to enable SSL for the connection between phpMyAdmin and the MySQL
server to secure the connection.</p>
<p>When using the <code class="docutils literal notranslate"><span class="pre">'mysql'</span></code> extension,
none of the remaining <code class="docutils literal notranslate"><span class="pre">'ssl...'</span></code> configuration options apply.</p>
<p>We strongly recommend the <code class="docutils literal notranslate"><span class="pre">'mysqli'</span></code> extension when using this option.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<a class="reference internal" href="#example-google-ssl"><span class="std std-ref">Google Cloud SQL with SSL</span></a>,
<a class="reference internal" href="#example-aws-ssl"><span class="std std-ref">Amazon RDS Aurora with SSL</span></a>,
<span class="target" id="index-16"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-17"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-18"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-19"></span><a class="reference internal" href="#cfg_Servers_ssl_ca_path"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca_path']</span></code></a>,
<span class="target" id="index-20"></span><a class="reference internal" href="#cfg_Servers_ssl_ciphers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ciphers']</span></code></a>,
<span class="target" id="index-21"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_ssl_key">
<code class="sig-name descname">$cfg['Servers'][$i]['ssl_key']</code><a class="headerlink" href="#cfg_Servers_ssl_key" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>NULL</p>
</dd>
</dl>
<p>Path to the client key file when using SSL for connecting to the MySQL
server. This is used to authenticate the client to the server.</p>
<p>For example:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl_key&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/etc/mysql/server-key.pem&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<a class="reference internal" href="#example-google-ssl"><span class="std std-ref">Google Cloud SQL with SSL</span></a>,
<a class="reference internal" href="#example-aws-ssl"><span class="std std-ref">Amazon RDS Aurora with SSL</span></a>,
<span class="target" id="index-22"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-23"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-24"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-25"></span><a class="reference internal" href="#cfg_Servers_ssl_ca_path"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca_path']</span></code></a>,
<span class="target" id="index-26"></span><a class="reference internal" href="#cfg_Servers_ssl_ciphers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ciphers']</span></code></a>,
<span class="target" id="index-27"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_ssl_cert">
<code class="sig-name descname">$cfg['Servers'][$i]['ssl_cert']</code><a class="headerlink" href="#cfg_Servers_ssl_cert" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>NULL</p>
</dd>
</dl>
<p>Path to the client certificate file when using SSL for connecting to the
MySQL server. This is used to authenticate the client to the server.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<a class="reference internal" href="#example-google-ssl"><span class="std std-ref">Google Cloud SQL with SSL</span></a>,
<a class="reference internal" href="#example-aws-ssl"><span class="std std-ref">Amazon RDS Aurora with SSL</span></a>,
<span class="target" id="index-28"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-29"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-30"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-31"></span><a class="reference internal" href="#cfg_Servers_ssl_ca_path"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca_path']</span></code></a>,
<span class="target" id="index-32"></span><a class="reference internal" href="#cfg_Servers_ssl_ciphers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ciphers']</span></code></a>,
<span class="target" id="index-33"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_ssl_ca">
<code class="sig-name descname">$cfg['Servers'][$i]['ssl_ca']</code><a class="headerlink" href="#cfg_Servers_ssl_ca" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>NULL</p>
</dd>
</dl>
<p>Path to the CA file when using SSL for connecting to the MySQL server.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<a class="reference internal" href="#example-google-ssl"><span class="std std-ref">Google Cloud SQL with SSL</span></a>,
<a class="reference internal" href="#example-aws-ssl"><span class="std std-ref">Amazon RDS Aurora with SSL</span></a>,
<span class="target" id="index-34"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-35"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-36"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-37"></span><a class="reference internal" href="#cfg_Servers_ssl_ca_path"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca_path']</span></code></a>,
<span class="target" id="index-38"></span><a class="reference internal" href="#cfg_Servers_ssl_ciphers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ciphers']</span></code></a>,
<span class="target" id="index-39"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_ssl_ca_path">
<code class="sig-name descname">$cfg['Servers'][$i]['ssl_ca_path']</code><a class="headerlink" href="#cfg_Servers_ssl_ca_path" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>NULL</p>
</dd>
</dl>
<p>Directory containing trusted SSL CA certificates in PEM format.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<a class="reference internal" href="#example-google-ssl"><span class="std std-ref">Google Cloud SQL with SSL</span></a>,
<a class="reference internal" href="#example-aws-ssl"><span class="std std-ref">Amazon RDS Aurora with SSL</span></a>,
<span class="target" id="index-40"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-41"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-42"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-43"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-44"></span><a class="reference internal" href="#cfg_Servers_ssl_ciphers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ciphers']</span></code></a>,
<span class="target" id="index-45"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_ssl_ciphers">
<code class="sig-name descname">$cfg['Servers'][$i]['ssl_ciphers']</code><a class="headerlink" href="#cfg_Servers_ssl_ciphers" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>NULL</p>
</dd>
</dl>
<p>List of allowable ciphers for SSL connections to the MySQL server.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<a class="reference internal" href="#example-google-ssl"><span class="std std-ref">Google Cloud SQL with SSL</span></a>,
<a class="reference internal" href="#example-aws-ssl"><span class="std std-ref">Amazon RDS Aurora with SSL</span></a>,
<span class="target" id="index-46"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-47"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-48"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-49"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-50"></span><a class="reference internal" href="#cfg_Servers_ssl_ca_path"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca_path']</span></code></a>,
<span class="target" id="index-51"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_ssl_verify">
<code class="sig-name descname">$cfg['Servers'][$i]['ssl_verify']</code><a class="headerlink" href="#cfg_Servers_ssl_verify" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.6.0: </span>This is supported since phpMyAdmin 4.6.0.</p>
</div>
<p>If your PHP install uses the MySQL Native Driver (mysqlnd), your
MySQL server is 5.6 or later, and your SSL certificate is self-signed,
there is a chance your SSL connection will fail due to validation.
Setting this to <code class="docutils literal notranslate"><span class="pre">false</span></code> will disable the validation check.</p>
<p>Since PHP 5.6.0 it also verifies whether server name matches CN of its
certificate. There is currently no way to disable just this check without
disabling complete SSL verification.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Disabling the certificate verification defeats purpose of using SSL.
This will make the connection vulnerable to man in the middle attacks.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This flag only works with PHP 5.6.16 or later.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<a class="reference internal" href="#example-google-ssl"><span class="std std-ref">Google Cloud SQL with SSL</span></a>,
<a class="reference internal" href="#example-aws-ssl"><span class="std std-ref">Amazon RDS Aurora with SSL</span></a>,
<span class="target" id="index-52"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-53"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-54"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-55"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-56"></span><a class="reference internal" href="#cfg_Servers_ssl_ca_path"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca_path']</span></code></a>,
<span class="target" id="index-57"></span><a class="reference internal" href="#cfg_Servers_ssl_ciphers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ciphers']</span></code></a>,
<span class="target" id="index-58"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_connect_type">
<code class="sig-name descname">$cfg['Servers'][$i]['connect_type']</code><a class="headerlink" href="#cfg_Servers_connect_type" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'tcp'</span></code></p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.7.0: </span>This setting is no longer used as of 4.7.0, since MySQL decides the
connection type based on host, so it could lead to unexpected results.
Please set <span class="target" id="index-59"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a> accordingly
instead.</p>
</div>
<p>What type connection to use with the MySQL server. Your options are
<code class="docutils literal notranslate"><span class="pre">'socket'</span></code> and <code class="docutils literal notranslate"><span class="pre">'tcp'</span></code>. It defaults to tcp as that is nearly guaranteed
to be available on all MySQL servers, while sockets are not supported on
some platforms. To use the socket mode, your MySQL server must be on the
same machine as the Web server.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_compress">
<code class="sig-name descname">$cfg['Servers'][$i]['compress']</code><a class="headerlink" href="#cfg_Servers_compress" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Whether to use a compressed protocol for the MySQL server connection
or not (experimental).</p>
</dd></dl>

<span class="target" id="controlhost"></span><dl class="config option">
<dt id="cfg_Servers_controlhost">
<code class="sig-name descname">$cfg['Servers'][$i]['controlhost']</code><a class="headerlink" href="#cfg_Servers_controlhost" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Permits to use an alternate host to hold the configuration storage
data.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-60"></span><a class="reference internal" href="#cfg_Servers_control_*"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['control_*']</span></code></a></p>
</div>
</dd></dl>

<span class="target" id="controlport"></span><dl class="config option">
<dt id="cfg_Servers_controlport">
<code class="sig-name descname">$cfg['Servers'][$i]['controlport']</code><a class="headerlink" href="#cfg_Servers_controlport" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Permits to use an alternate port to connect to the host that
holds the configuration storage.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-61"></span><a class="reference internal" href="#cfg_Servers_control_*"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['control_*']</span></code></a></p>
</div>
</dd></dl>

<span class="target" id="controluser"></span><dl class="config option">
<dt id="cfg_Servers_controluser">
<code class="sig-name descname">$cfg['Servers'][$i]['controluser']</code><a class="headerlink" href="#cfg_Servers_controluser" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_controlpass">
<code class="sig-name descname">$cfg['Servers'][$i]['controlpass']</code><a class="headerlink" href="#cfg_Servers_controlpass" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>This special account is used to access <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a>.
You don’t need it in single user case, but if phpMyAdmin is shared it
is recommended to give access to <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a> only to this user
and configure phpMyAdmin to use it. All users will then be able to use
the features without need to have direct access to <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 2.2.5: </span>those were called <code class="docutils literal notranslate"><span class="pre">stduser</span></code> and <code class="docutils literal notranslate"><span class="pre">stdpass</span></code></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#setup"><span class="std std-ref">Installation</span></a>,
<a class="reference internal" href="setup.html#authentication-modes"><span class="std std-ref">Using authentication modes</span></a>,
<a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a>,
<span class="target" id="index-62"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a>,
<span class="target" id="index-63"></span><a class="reference internal" href="#cfg_Servers_controlhost"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['controlhost']</span></code></a>,
<span class="target" id="index-64"></span><a class="reference internal" href="#cfg_Servers_controlport"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['controlport']</span></code></a>,
<span class="target" id="index-65"></span><a class="reference internal" href="#cfg_Servers_control_*"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['control_*']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_control_*">
<code class="sig-name descname">$cfg['Servers'][$i]['control_*']</code><a class="headerlink" href="#cfg_Servers_control_*" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>mixed</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.7.0.</span></p>
</div>
<p>You can change any MySQL connection setting for control link (used to
access <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a>) using configuration prefixed with <code class="docutils literal notranslate"><span class="pre">control_</span></code>.</p>
<p>This can be used to change any aspect of the control connection, which by
default uses same parameters as the user one.</p>
<p>For example you can configure SSL for the control connection:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="c1">// Enable SSL</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;control_ssl&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">true</span><span class="p">;</span>
<span class="c1">// Client secret key</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;control_ssl_key&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;../client-key.pem&#39;</span><span class="p">;</span>
<span class="c1">// Client certificate</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;control_ssl_cert&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;../client-cert.pem&#39;</span><span class="p">;</span>
<span class="c1">// Server certification authority</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;control_ssl_ca&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;../server-ca.pem&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-66"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-67"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-68"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-69"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-70"></span><a class="reference internal" href="#cfg_Servers_ssl_ca_path"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca_path']</span></code></a>,
<span class="target" id="index-71"></span><a class="reference internal" href="#cfg_Servers_ssl_ciphers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ciphers']</span></code></a>,
<span class="target" id="index-72"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_auth_type">
<code class="sig-name descname">$cfg['Servers'][$i]['auth_type']</code><a class="headerlink" href="#cfg_Servers_auth_type" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'cookie'</span></code></p>
</dd>
</dl>
<p>Whether config or cookie or <a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> or signon authentication should be
used for this server.</p>
<ul class="simple">
<li><p>‘config’ authentication (<code class="docutils literal notranslate"><span class="pre">$auth_type</span> <span class="pre">=</span> <span class="pre">'config'</span></code>) is the plain old
way: username and password are stored in <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code>.</p></li>
<li><p>‘cookie’ authentication mode (<code class="docutils literal notranslate"><span class="pre">$auth_type</span> <span class="pre">=</span> <span class="pre">'cookie'</span></code>) allows you to
log in as any valid MySQL user with the help of cookies.</p></li>
<li><p>‘http’ authentication allows you to log in as any
valid MySQL user via HTTP-Auth.</p></li>
<li><p>‘signon’ authentication mode (<code class="docutils literal notranslate"><span class="pre">$auth_type</span> <span class="pre">=</span> <span class="pre">'signon'</span></code>) allows you to
log in from prepared PHP session data or using supplied PHP script.</p></li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#authentication-modes"><span class="std std-ref">Using authentication modes</span></a></p>
</div>
</dd></dl>

<span class="target" id="servers-auth-http-realm"></span><dl class="config option">
<dt id="cfg_Servers_auth_http_realm">
<code class="sig-name descname">$cfg['Servers'][$i]['auth_http_realm']</code><a class="headerlink" href="#cfg_Servers_auth_http_realm" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>When using auth_type = <code class="docutils literal notranslate"><span class="pre">http</span></code>, this field allows to define a custom
<a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> Basic Auth Realm which will be displayed to the user. If not
explicitly specified in your configuration, a string combined of
“phpMyAdmin ” and either <span class="target" id="index-73"></span><a class="reference internal" href="#cfg_Servers_verbose"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['verbose']</span></code></a> or
<span class="target" id="index-74"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a> will be used.</p>
</dd></dl>

<span class="target" id="servers-auth-swekey-config"></span><dl class="config option">
<dt id="cfg_Servers_auth_swekey_config">
<code class="sig-name descname">$cfg['Servers'][$i]['auth_swekey_config']</code><a class="headerlink" href="#cfg_Servers_auth_swekey_config" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version *******: </span>This setting was named <cite>$cfg[‘Servers’][$i][‘auth_feebee_config’]</cite> and was renamed before the <cite>*******</cite> release.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.6.4: </span>This setting was removed because their servers are no longer working and it was not working correctly.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.0.10.17: </span>This setting was removed in a maintenance release because their servers are no longer working and it was not working correctly.</p>
</div>
<p>The name of the file containing swekey ids and login names for hardware
authentication. Leave empty to deactivate this feature.</p>
</dd></dl>

<span class="target" id="servers-user"></span><dl class="config option">
<dt id="cfg_Servers_user">
<code class="sig-name descname">$cfg['Servers'][$i]['user']</code><a class="headerlink" href="#cfg_Servers_user" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'root'</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_password">
<code class="sig-name descname">$cfg['Servers'][$i]['password']</code><a class="headerlink" href="#cfg_Servers_password" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>When using <span class="target" id="index-75"></span><a class="reference internal" href="#cfg_Servers_auth_type"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['auth_type']</span></code></a> set to
‘config’, this is the user/password-pair which phpMyAdmin will use to
connect to the MySQL server. This user/password pair is not needed when
<a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> or cookie authentication is used
and should be empty.</p>
</dd></dl>

<span class="target" id="servers-nopassword"></span><dl class="config option">
<dt id="cfg_Servers_nopassword">
<code class="sig-name descname">$cfg['Servers'][$i]['nopassword']</code><a class="headerlink" href="#cfg_Servers_nopassword" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.7.0: </span>This setting was removed as it can produce unexpected results.</p>
</div>
<p>Allow attempt to log in without password when a login with password
fails. This can be used together with http authentication, when
authentication is done some other way and phpMyAdmin gets user name
from auth and uses empty password for connecting to MySQL. Password
login is still tried first, but as fallback, no password method is
tried.</p>
</dd></dl>

<span class="target" id="servers-only-db"></span><dl class="config option">
<dt id="cfg_Servers_only_db">
<code class="sig-name descname">$cfg['Servers'][$i]['only_db']</code><a class="headerlink" href="#cfg_Servers_only_db" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>If set to a (an array of) database name(s), only this (these)
database(s) will be shown to the user. Since phpMyAdmin 2.2.1,
this/these database(s) name(s) may contain MySQL wildcards characters
(“_” and “%”): if you want to use literal instances of these
characters, escape them (I.E. use <code class="docutils literal notranslate"><span class="pre">'my\_db'</span></code> and not <code class="docutils literal notranslate"><span class="pre">'my_db'</span></code>).</p>
<p>This setting is an efficient way to lower the server load since the
latter does not need to send MySQL requests to build the available
database list. But <strong>it does not replace the privileges rules of the
MySQL database server</strong>. If set, it just means only these databases
will be displayed but <strong>not that all other databases can’t be used.</strong></p>
<p>An example of using more that one database:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;only_db&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;db1&#39;</span><span class="p">,</span> <span class="s1">&#39;db2&#39;</span><span class="p">];</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 4.0.0: </span>Previous versions permitted to specify the display order of
the database names via this directive.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_hide_db">
<code class="sig-name descname">$cfg['Servers'][$i]['hide_db']</code><a class="headerlink" href="#cfg_Servers_hide_db" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Regular expression for hiding some databases from unprivileged users.
This only hides them from listing, but a user is still able to access
them (using, for example, the SQL query area). To limit access, use
the MySQL privilege system.  For example, to hide all databases
starting with the letter “a”, use</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;hide_db&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;^a&#39;</span><span class="p">;</span>
</pre></div>
</div>
<p>and to hide both “db1” and “db2” use</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;hide_db&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;^(db1|db2)$&#39;</span><span class="p">;</span>
</pre></div>
</div>
<p>More information on regular expressions can be found in the <a class="reference external" href="https://www.php.net/manual/en/reference.pcre.pattern.syntax.php">PCRE
pattern syntax</a> portion
of the PHP reference manual.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_verbose">
<code class="sig-name descname">$cfg['Servers'][$i]['verbose']</code><a class="headerlink" href="#cfg_Servers_verbose" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Only useful when using phpMyAdmin with multiple server entries. If
set, this string will be displayed instead of the hostname in the
pull-down menu on the main page. This can be useful if you want to
show only certain databases on your system, for example. For HTTP
auth, all non-US-ASCII characters will be stripped.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_extension">
<code class="sig-name descname">$cfg['Servers'][$i]['extension']</code><a class="headerlink" href="#cfg_Servers_extension" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'mysqli'</span></code></p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.2.0: </span>This setting was removed. The <code class="docutils literal notranslate"><span class="pre">mysql</span></code> extension will only be used when
the <code class="docutils literal notranslate"><span class="pre">mysqli</span></code> extension is not available. As of 5.0.0, only the
<code class="docutils literal notranslate"><span class="pre">mysqli</span></code> extension can be used.</p>
</div>
<p>The PHP MySQL extension to use (<code class="docutils literal notranslate"><span class="pre">mysql</span></code> or <code class="docutils literal notranslate"><span class="pre">mysqli</span></code>).</p>
<p>It is recommended to use <code class="docutils literal notranslate"><span class="pre">mysqli</span></code> in all installations.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_pmadb">
<code class="sig-name descname">$cfg['Servers'][$i]['pmadb']</code><a class="headerlink" href="#cfg_Servers_pmadb" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The name of the database containing the phpMyAdmin configuration
storage.</p>
<p>See the <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a>  section in this document to see the benefits of
this feature, and for a quick way of creating this database and the needed
tables.</p>
<p>If you are the only user of this phpMyAdmin installation, you can use your
current database to store those special tables; in this case, just put your
current database name in <span class="target" id="index-76"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a>. For a
multi-user installation, set this parameter to the name of your central
database containing the phpMyAdmin configuration storage.</p>
</dd></dl>

<span class="target" id="bookmark"></span><dl class="config option">
<dt id="cfg_Servers_bookmarktable">
<code class="sig-name descname">$cfg['Servers'][$i]['bookmarktable']</code><a class="headerlink" href="#cfg_Servers_bookmarktable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 2.2.0.</span></p>
</div>
<p>Since release 2.2.0 phpMyAdmin allows users to bookmark queries. This
can be useful for queries you often run. To allow the usage of this
functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-77"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>enter the table name in <span class="target" id="index-78"></span><a class="reference internal" href="#cfg_Servers_bookmarktable"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['bookmarktable']</span></code></a></p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="relation"></span><dl class="config option">
<dt id="cfg_Servers_relation">
<code class="sig-name descname">$cfg['Servers'][$i]['relation']</code><a class="headerlink" href="#cfg_Servers_relation" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 2.2.4.</span></p>
</div>
<p>Since release 2.2.4 you can describe, in a special ‘relation’ table,
which column is a key in another table (a foreign key). phpMyAdmin
currently uses this to:</p>
<ul class="simple">
<li><p>make clickable, when you browse the master table, the data values that
point to the foreign table;</p></li>
<li><p>display in an optional tool-tip the “display column” when browsing the
master table, if you move the mouse to a column containing a foreign
key (use also the ‘table_info’ table); (see <a class="reference internal" href="faq.html#faqdisplay"><span class="std std-ref">6.7 How can I use the “display column” feature?</span></a>)</p></li>
<li><p>in edit/insert mode, display a drop-down list of possible foreign keys
(key value and “display column” are shown) (see <a class="reference internal" href="faq.html#faq6-21"><span class="std std-ref">6.21 In edit/insert mode, how can I see a list of possible values for a column, based on some foreign table?</span></a>)</p></li>
<li><p>display links on the table properties page, to check referential
integrity (display missing foreign keys) for each described key;</p></li>
<li><p>in query-by-example, create automatic joins (see <a class="reference internal" href="faq.html#faq6-6"><span class="std std-ref">6.6 How can I use the relation table in Query-by-example?</span></a>)</p></li>
<li><p>enable you to get a <a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a> schema of
your database (also uses the table_coords table).</p></li>
</ul>
<p>The keys can be numeric or character.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-79"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the relation table name in <span class="target" id="index-80"></span><a class="reference internal" href="#cfg_Servers_relation"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['relation']</span></code></a></p></li>
<li><p>now as normal user open phpMyAdmin and for each one of your tables
where you want to use this feature, click <span class="guilabel">Structure/Relation view/</span>
and choose foreign columns.</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In the current version, <code class="docutils literal notranslate"><span class="pre">master_db</span></code> must be the same as <code class="docutils literal notranslate"><span class="pre">foreign_db</span></code>.
Those columns have been put in future development of the cross-db
relations.</p>
</div>
</dd></dl>

<span class="target" id="table-info"></span><dl class="config option">
<dt id="cfg_Servers_table_info">
<code class="sig-name descname">$cfg['Servers'][$i]['table_info']</code><a class="headerlink" href="#cfg_Servers_table_info" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 2.3.0.</span></p>
</div>
<p>Since release 2.3.0 you can describe, in a special ‘table_info’
table, which column is to be displayed as a tool-tip when moving the
cursor over the corresponding key. This configuration variable will
hold the name of this special table. To allow the usage of this
functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-81"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-82"></span><a class="reference internal" href="#cfg_Servers_table_info"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['table_info']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__table_info</span></code>)</p></li>
<li><p>then for each table where you want to use this feature, click
“Structure/Relation view/Choose column to display” to choose the
column.</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faqdisplay"><span class="std std-ref">6.7 How can I use the “display column” feature?</span></a></p>
</div>
</dd></dl>

<span class="target" id="table-coords"></span><dl class="config option">
<dt id="cfg_Servers_table_coords">
<code class="sig-name descname">$cfg['Servers'][$i]['table_coords']</code><a class="headerlink" href="#cfg_Servers_table_coords" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The designer feature can save your page layout; by pressing the “Save page” or “Save page as”
button in the expanding designer menu, you can customize the layout and have it loaded the next
time you use the designer. That layout is stored in this table. Furthermore, this table is also
required for using the PDF relation export feature, see
<span class="target" id="index-83"></span><a class="reference internal" href="#cfg_Servers_pdf_pages"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pdf_pages']</span></code></a> for additional details.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_pdf_pages">
<code class="sig-name descname">$cfg['Servers'][$i]['pdf_pages']</code><a class="headerlink" href="#cfg_Servers_pdf_pages" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 2.3.0.</span></p>
</div>
<p>Since release 2.3.0 you can have phpMyAdmin create <a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a> pages
showing the relations between your tables. Further, the designer interface
permits visually managing the relations. To do this it needs two tables
“pdf_pages” (storing information about the available <a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a> pages)
and “table_coords” (storing coordinates where each table will be placed on
a <a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a> schema output).  You must be using the “relation” feature.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-84"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the correct table names in
<span class="target" id="index-85"></span><a class="reference internal" href="#cfg_Servers_table_coords"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['table_coords']</span></code></a> and
<span class="target" id="index-86"></span><a class="reference internal" href="#cfg_Servers_pdf_pages"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pdf_pages']</span></code></a></p></li>
</ul>
<p>This feature can be disabled by setting either of the configurations to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faqpdf"><span class="std std-ref">6.8 How can I produce a PDF schema of my database?</span></a>.</p>
</div>
</dd></dl>

<span class="target" id="designer-coords"></span><dl class="config option">
<dt id="cfg_Servers_designer_coords">
<code class="sig-name descname">$cfg['Servers'][$i]['designer_coords']</code><a class="headerlink" href="#cfg_Servers_designer_coords" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 2.10.0: </span>Since release 2.10.0 a Designer interface is available; it permits to
visually manage the relations.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.3.0: </span>This setting was removed and the Designer table positioning data is now stored into <span class="target" id="index-87"></span><a class="reference internal" href="#cfg_Servers_table_coords"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['table_coords']</span></code></a>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You can now delete the table <cite>pma__designer_coords</cite> from your phpMyAdmin configuration storage database and remove <span class="target" id="index-88"></span><a class="reference internal" href="#cfg_Servers_designer_coords"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['designer_coords']</span></code></a> from your configuration file.</p>
</div>
</dd></dl>

<span class="target" id="col-com"></span><dl class="config option">
<dt id="cfg_Servers_column_info">
<code class="sig-name descname">$cfg['Servers'][$i]['column_info']</code><a class="headerlink" href="#cfg_Servers_column_info" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 2.3.0.</span></p>
</div>
<p>This part requires a content update!  Since release 2.3.0 you can
store comments to describe each column for each table. These will then
be shown on the “printview”.</p>
<p>Starting with release 2.5.0, comments are consequently used on the table
property pages and table browse view, showing up as tool-tips above the
column name (properties page) or embedded within the header of table in
browse view. They can also be shown in a table dump. Please see the
relevant configuration directives later on.</p>
<p>Also new in release 2.5.0 is a MIME- transformation system which is also
based on the following table structure. See <a class="reference internal" href="transformations.html#transformations"><span class="std std-ref">Transformations</span></a> for
further information. To use the MIME- transformation system, your
column_info table has to have the three new columns ‘mimetype’,
‘transformation’, ‘transformation_options’.</p>
<p>Starting with release 4.3.0, a new input-oriented transformation system
has been introduced. Also, backward compatibility code used in the old
transformations system was removed. As a result, an update to column_info
table is necessary for previous transformations and the new input-oriented
transformation system to work. phpMyAdmin will upgrade it automatically
for you by analyzing your current column_info table structure.
However, if something goes wrong with the auto-upgrade then you can
use the SQL script found in <code class="docutils literal notranslate"><span class="pre">./sql/upgrade_column_info_4_3_0+.sql</span></code>
to upgrade it manually.</p>
<p>To allow the usage of this functionality:</p>
<ul>
<li><p>set up <span class="target" id="index-89"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-90"></span><a class="reference internal" href="#cfg_Servers_column_info"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['column_info']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__column_info</span></code>)</p></li>
<li><p>to update your PRE-2.5.0 Column_comments table use this:  and
remember that the Variable in <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> has been renamed from
<code class="samp docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['column_comments']</span></code> to
<span class="target" id="index-91"></span><a class="reference internal" href="#cfg_Servers_column_info"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['column_info']</span></code></a></p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span> <span class="k">TABLE</span> <span class="n">`pma__column_comments`</span>
<span class="k">ADD</span> <span class="n">`mimetype`</span> <span class="kt">VARCHAR</span><span class="p">(</span> <span class="mi">255</span> <span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
<span class="k">ADD</span> <span class="n">`transformation`</span> <span class="kt">VARCHAR</span><span class="p">(</span> <span class="mi">255</span> <span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
<span class="k">ADD</span> <span class="n">`transformation_options`</span> <span class="kt">VARCHAR</span><span class="p">(</span> <span class="mi">255</span> <span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">;</span>
</pre></div>
</div>
</li>
<li><p>to update your PRE-4.3.0 Column_info table manually use this
<code class="docutils literal notranslate"><span class="pre">./sql/upgrade_column_info_4_3_0+.sql</span></code> SQL script.</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For auto-upgrade functionality to work, your
<span class="target" id="index-92"></span><a class="reference internal" href="#cfg_Servers_controluser"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['controluser']</span></code></a> must have ALTER privilege on
<code class="docutils literal notranslate"><span class="pre">phpmyadmin</span></code> database. See the <a class="reference external" href="https://dev.mysql.com/doc/refman/8.0/en/grant.html">MySQL documentation for GRANT</a> on how to
<code class="docutils literal notranslate"><span class="pre">GRANT</span></code> privileges to a user.</p>
</div>
</dd></dl>

<span class="target" id="history"></span><dl class="config option">
<dt id="cfg_Servers_history">
<code class="sig-name descname">$cfg['Servers'][$i]['history']</code><a class="headerlink" href="#cfg_Servers_history" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 2.5.0.</span></p>
</div>
<p>Since release 2.5.0 you can store your <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> history, which means all
queries you entered manually into the phpMyAdmin interface. If you don’t
want to use a table-based history, you can use the JavaScript-based
history.</p>
<p>Using that, all your history items are deleted when closing the window.
Using <span class="target" id="index-93"></span><a class="reference internal" href="#cfg_QueryHistoryMax"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['QueryHistoryMax']</span></code></a> you can specify an amount of
history items you want to have on hold. On every login, this list gets cut
to the maximum amount.</p>
<p>The query history is only available if JavaScript is enabled in
your browser.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-94"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-95"></span><a class="reference internal" href="#cfg_Servers_history"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['history']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__history</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="recent"></span><dl class="config option">
<dt id="cfg_Servers_recent">
<code class="sig-name descname">$cfg['Servers'][$i]['recent']</code><a class="headerlink" href="#cfg_Servers_recent" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.0.</span></p>
</div>
<p>Since release 3.5.0 you can show recently used tables in the
navigation panel. It helps you to jump across table directly, without
the need to select the database, and then select the table. Using
<span class="target" id="index-96"></span><a class="reference internal" href="#cfg_NumRecentTables"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NumRecentTables']</span></code></a> you can configure the maximum number
of recent tables shown. When you select a table from the list, it will jump to
the page specified in <span class="target" id="index-97"></span><a class="reference internal" href="#cfg_NavigationTreeDefaultTabTable"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NavigationTreeDefaultTabTable']</span></code></a>.</p>
<p>Without configuring the storage, you can still access the recently used tables,
but it will disappear after you logout.</p>
<p>To allow the usage of this functionality persistently:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-98"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-99"></span><a class="reference internal" href="#cfg_Servers_recent"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['recent']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__recent</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="favorite"></span><dl class="config option">
<dt id="cfg_Servers_favorite">
<code class="sig-name descname">$cfg['Servers'][$i]['favorite']</code><a class="headerlink" href="#cfg_Servers_favorite" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.2.0.</span></p>
</div>
<p>Since release 4.2.0 you can show a list of selected tables in the
navigation panel. It helps you to jump to the table directly, without
the need to select the database, and then select the table. When you
select a table from the list, it will jump to the page specified in
<span class="target" id="index-100"></span><a class="reference internal" href="#cfg_NavigationTreeDefaultTabTable"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NavigationTreeDefaultTabTable']</span></code></a>.</p>
<p>You can add tables to this list or remove tables from it in database
structure page by clicking on the star icons next to table names. Using
<span class="target" id="index-101"></span><a class="reference internal" href="#cfg_NumFavoriteTables"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NumFavoriteTables']</span></code></a> you can configure the maximum
number of favorite tables shown.</p>
<p>Without configuring the storage, you can still access the favorite tables,
but it will disappear after you logout.</p>
<p>To allow the usage of this functionality persistently:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-102"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-103"></span><a class="reference internal" href="#cfg_Servers_favorite"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['favorite']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__favorite</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="table-uiprefs"></span><dl class="config option">
<dt id="cfg_Servers_table_uiprefs">
<code class="sig-name descname">$cfg['Servers'][$i]['table_uiprefs']</code><a class="headerlink" href="#cfg_Servers_table_uiprefs" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.0.</span></p>
</div>
<p>Since release 3.5.0 phpMyAdmin can be configured to remember several
things (sorted column <span class="target" id="index-104"></span><a class="reference internal" href="#cfg_RememberSorting"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['RememberSorting']</span></code></a>, column order,
and column visibility from a database table) for browsing tables. Without
configuring the storage, these features still can be used, but the values will
disappear after you logout.</p>
<p>To allow the usage of these functionality persistently:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-105"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-106"></span><a class="reference internal" href="#cfg_Servers_table_uiprefs"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['table_uiprefs']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__table_uiprefs</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_users">
<code class="sig-name descname">$cfg['Servers'][$i]['users']</code><a class="headerlink" href="#cfg_Servers_users" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The table used by phpMyAdmin to store user name information for associating with user groups.
See the next entry on <span class="target" id="index-107"></span><a class="reference internal" href="#cfg_Servers_usergroups"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['usergroups']</span></code></a> for more details
and the suggested settings.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_usergroups">
<code class="sig-name descname">$cfg['Servers'][$i]['usergroups']</code><a class="headerlink" href="#cfg_Servers_usergroups" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.1.0.</span></p>
</div>
<p>Since release 4.1.0 you can create different user groups with menu items
attached to them. Users can be assigned to these groups and the logged in
user would only see menu items configured to the usergroup they are assigned to.
To do this it needs two tables “usergroups” (storing allowed menu items for each
user group) and “users” (storing users and their assignments to user groups).</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-108"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the correct table names in
<span class="target" id="index-109"></span><a class="reference internal" href="#cfg_Servers_users"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['users']</span></code></a> (e.g. <code class="docutils literal notranslate"><span class="pre">pma__users</span></code>) and
<span class="target" id="index-110"></span><a class="reference internal" href="#cfg_Servers_usergroups"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['usergroups']</span></code></a> (e.g. <code class="docutils literal notranslate"><span class="pre">pma__usergroups</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting either of the configurations to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="privileges.html#configurablemenus"><span class="std std-ref">Configurable menus and user groups</span></a></p>
</div>
</dd></dl>

<span class="target" id="navigationhiding"></span><dl class="config option">
<dt id="cfg_Servers_navigationhiding">
<code class="sig-name descname">$cfg['Servers'][$i]['navigationhiding']</code><a class="headerlink" href="#cfg_Servers_navigationhiding" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.1.0.</span></p>
</div>
<p>Since release 4.1.0 you can hide/show items in the navigation tree.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-111"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-112"></span><a class="reference internal" href="#cfg_Servers_navigationhiding"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['navigationhiding']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__navigationhiding</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="central-columns"></span><dl class="config option">
<dt id="cfg_Servers_central_columns">
<code class="sig-name descname">$cfg['Servers'][$i]['central_columns']</code><a class="headerlink" href="#cfg_Servers_central_columns" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.3.0.</span></p>
</div>
<p>Since release 4.3.0 you can have a central list of columns per database.
You can add/remove columns to the list as per your requirement. These columns
in the central list will be available to use while you create a new column for
a table or create a table itself. You can select a column from central list
while creating a new column, it will save you from writing the same column definition
over again or from writing different names for similar column.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-113"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-114"></span><a class="reference internal" href="#cfg_Servers_central_columns"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['central_columns']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__central_columns</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="designer-settings"></span><dl class="config option">
<dt id="cfg_Servers_designer_settings">
<code class="sig-name descname">$cfg['Servers'][$i]['designer_settings']</code><a class="headerlink" href="#cfg_Servers_designer_settings" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.5.0.</span></p>
</div>
<p>Since release 4.5.0 your designer settings can be remembered.
Your choice regarding ‘Angular/Direct Links’, ‘Snap to Grid’, ‘Toggle Relation Lines’,
‘Small/Big All’, ‘Move Menu’ and ‘Pin Text’ can be remembered persistently.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-115"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-116"></span><a class="reference internal" href="#cfg_Servers_designer_settings"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['designer_settings']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__designer_settings</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="savedsearches"></span><dl class="config option">
<dt id="cfg_Servers_savedsearches">
<code class="sig-name descname">$cfg['Servers'][$i]['savedsearches']</code><a class="headerlink" href="#cfg_Servers_savedsearches" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.2.0.</span></p>
</div>
<p>Since release 4.2.0 you can save and load query-by-example searches from the Database &gt; Query panel.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-117"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-118"></span><a class="reference internal" href="#cfg_Servers_savedsearches"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['savedsearches']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__savedsearches</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="export-templates"></span><dl class="config option">
<dt id="cfg_Servers_export_templates">
<code class="sig-name descname">$cfg['Servers'][$i]['export_templates']</code><a class="headerlink" href="#cfg_Servers_export_templates" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.5.0.</span></p>
</div>
<p>Since release 4.5.0 you can save and load export templates.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-119"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-120"></span><a class="reference internal" href="#cfg_Servers_export_templates"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['export_templates']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__export_templates</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="tracking"></span><dl class="config option">
<dt id="cfg_Servers_tracking">
<code class="sig-name descname">$cfg['Servers'][$i]['tracking']</code><a class="headerlink" href="#cfg_Servers_tracking" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.x.</span></p>
</div>
<p>Since release 3.3.x a tracking mechanism is available. It helps you to
track every <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> command which is
executed by phpMyAdmin. The mechanism supports logging of data
manipulation and data definition statements. After enabling it you can
create versions of tables.</p>
<p>The creation of a version has two effects:</p>
<ul class="simple">
<li><p>phpMyAdmin saves a snapshot of the table, including structure and
indexes.</p></li>
<li><p>phpMyAdmin logs all commands which change the structure and/or data of
the table and links these commands with the version number.</p></li>
</ul>
<p>Of course you can view the tracked changes. On the <span class="guilabel">Tracking</span>
page a complete report is available for every version. For the report you
can use filters, for example you can get a list of statements within a date
range. When you want to filter usernames you can enter * for all names or
you enter a list of names separated by ‘,’. In addition you can export the
(filtered) report to a file or to a temporary database.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-121"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-122"></span><a class="reference internal" href="#cfg_Servers_tracking"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['tracking']</span></code></a> (e.g.
<code class="docutils literal notranslate"><span class="pre">pma__tracking</span></code>)</p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<span class="target" id="tracking2"></span><dl class="config option">
<dt id="cfg_Servers_tracking_version_auto_create">
<code class="sig-name descname">$cfg['Servers'][$i]['tracking_version_auto_create']</code><a class="headerlink" href="#cfg_Servers_tracking_version_auto_create" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Whether the tracking mechanism creates versions for tables and views
automatically.</p>
<p>If this is set to true and you create a table or view with</p>
<ul class="simple">
<li><p>CREATE TABLE …</p></li>
<li><p>CREATE VIEW …</p></li>
</ul>
<p>and no version exists for it, the mechanism will create a version for
you automatically.</p>
</dd></dl>

<span class="target" id="tracking3"></span><dl class="config option">
<dt id="cfg_Servers_tracking_default_statements">
<code class="sig-name descname">$cfg['Servers'][$i]['tracking_default_statements']</code><a class="headerlink" href="#cfg_Servers_tracking_default_statements" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'CREATE</span> <span class="pre">TABLE,ALTER</span> <span class="pre">TABLE,DROP</span> <span class="pre">TABLE,RENAME</span> <span class="pre">TABLE,CREATE</span> <span class="pre">INDEX,DROP</span> <span class="pre">INDEX,INSERT,UPDATE,DELETE,TRUNCATE,REPLACE,CREATE</span> <span class="pre">VIEW,ALTER</span> <span class="pre">VIEW,DROP</span> <span class="pre">VIEW,CREATE</span> <span class="pre">DATABASE,ALTER</span> <span class="pre">DATABASE,DROP</span> <span class="pre">DATABASE'</span></code></p>
</dd>
</dl>
<p>Defines the list of statements the auto-creation uses for new
versions.</p>
</dd></dl>

<span class="target" id="tracking4"></span><dl class="config option">
<dt id="cfg_Servers_tracking_add_drop_view">
<code class="sig-name descname">$cfg['Servers'][$i]['tracking_add_drop_view']</code><a class="headerlink" href="#cfg_Servers_tracking_add_drop_view" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether a <cite>DROP VIEW IF EXISTS</cite> statement will be added as first line to
the log when creating a view.</p>
</dd></dl>

<span class="target" id="tracking5"></span><dl class="config option">
<dt id="cfg_Servers_tracking_add_drop_table">
<code class="sig-name descname">$cfg['Servers'][$i]['tracking_add_drop_table']</code><a class="headerlink" href="#cfg_Servers_tracking_add_drop_table" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether a <cite>DROP TABLE IF EXISTS</cite> statement will be added as first line
to the log when creating a table.</p>
</dd></dl>

<span class="target" id="tracking6"></span><dl class="config option">
<dt id="cfg_Servers_tracking_add_drop_database">
<code class="sig-name descname">$cfg['Servers'][$i]['tracking_add_drop_database']</code><a class="headerlink" href="#cfg_Servers_tracking_add_drop_database" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether a <cite>DROP DATABASE IF EXISTS</cite> statement will be added as first
line to the log when creating a database.</p>
</dd></dl>

<span class="target" id="userconfig"></span><dl class="config option">
<dt id="cfg_Servers_userconfig">
<code class="sig-name descname">$cfg['Servers'][$i]['userconfig']</code><a class="headerlink" href="#cfg_Servers_userconfig" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or false</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.x.</span></p>
</div>
<p>Since release 3.4.x phpMyAdmin allows users to set most preferences by
themselves and store them in the database.</p>
<p>If you don’t allow for storing preferences in
<span class="target" id="index-123"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a>, users can still personalize
phpMyAdmin, but settings will be saved in browser’s local storage, or, it
is is unavailable, until the end of session.</p>
<p>To allow the usage of this functionality:</p>
<ul class="simple">
<li><p>set up <span class="target" id="index-124"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and the phpMyAdmin configuration storage</p></li>
<li><p>put the table name in <span class="target" id="index-125"></span><a class="reference internal" href="#cfg_Servers_userconfig"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['userconfig']</span></code></a></p></li>
</ul>
<p>This feature can be disabled by setting the configuration to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_MaxTableUiprefs">
<code class="sig-name descname">$cfg['Servers'][$i]['MaxTableUiprefs']</code><a class="headerlink" href="#cfg_Servers_MaxTableUiprefs" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>100</p>
</dd>
</dl>
<p>Maximum number of rows saved in
<span class="target" id="index-126"></span><a class="reference internal" href="#cfg_Servers_table_uiprefs"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['table_uiprefs']</span></code></a> table.</p>
<p>When tables are dropped or renamed,
<span class="target" id="index-127"></span><a class="reference internal" href="#cfg_Servers_table_uiprefs"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['table_uiprefs']</span></code></a> may contain invalid data
(referring to tables which no longer exist). We only keep this number of newest
rows in <span class="target" id="index-128"></span><a class="reference internal" href="#cfg_Servers_table_uiprefs"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['table_uiprefs']</span></code></a> and automatically
delete older rows.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_SessionTimeZone">
<code class="sig-name descname">$cfg['Servers'][$i]['SessionTimeZone']</code><a class="headerlink" href="#cfg_Servers_SessionTimeZone" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Sets the time zone used by phpMyAdmin. Leave blank to use the time zone of your
database server. Possible values are explained at
<a class="reference external" href="https://dev.mysql.com/doc/refman/8.0/en/time-zone-support.html">https://dev.mysql.com/doc/refman/8.0/en/time-zone-support.html</a></p>
<p>This is useful when your database server uses a time zone which is different from the
time zone you want to use in phpMyAdmin.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_AllowRoot">
<code class="sig-name descname">$cfg['Servers'][$i]['AllowRoot']</code><a class="headerlink" href="#cfg_Servers_AllowRoot" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to allow root access. This is just a shortcut for the
<span class="target" id="index-129"></span><a class="reference internal" href="#cfg_Servers_AllowDeny_rules"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['AllowDeny']['rules']</span></code></a> below.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_AllowNoPassword">
<code class="sig-name descname">$cfg['Servers'][$i]['AllowNoPassword']</code><a class="headerlink" href="#cfg_Servers_AllowNoPassword" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Whether to allow logins without a password. The default value of
<code class="docutils literal notranslate"><span class="pre">false</span></code> for this parameter prevents unintended access to a MySQL
server with was left with an empty password for root or on which an
anonymous (blank) user is defined.</p>
</dd></dl>

<span class="target" id="servers-allowdeny-order"></span><dl class="config option">
<dt id="cfg_Servers_AllowDeny_order">
<code class="sig-name descname">$cfg['Servers'][$i]['AllowDeny']['order']</code><a class="headerlink" href="#cfg_Servers_AllowDeny_order" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>If your rule order is empty, then <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a>
authorization is disabled.</p>
<p>If your rule order is set to
<code class="docutils literal notranslate"><span class="pre">'deny,allow'</span></code> then the system applies all deny rules followed by
allow rules. Access is allowed by default. Any client which does not
match a Deny command or does match an Allow command will be allowed
access to the server.</p>
<p>If your rule order is set to <code class="docutils literal notranslate"><span class="pre">'allow,deny'</span></code>
then the system applies all allow rules followed by deny rules. Access
is denied by default. Any client which does not match an Allow
directive or does match a Deny directive will be denied access to the
server.</p>
<p>If your rule order is set to <code class="docutils literal notranslate"><span class="pre">'explicit'</span></code>, authorization is
performed in a similar fashion to rule order ‘deny,allow’, with the
added restriction that your host/username combination <strong>must</strong> be
listed in the <em>allow</em> rules, and not listed in the <em>deny</em> rules. This
is the <strong>most</strong> secure means of using Allow/Deny rules, and was
available in Apache by specifying allow and deny rules without setting
any order.</p>
<p>Please also see <span class="target" id="index-130"></span><a class="reference internal" href="#cfg_TrustedProxies"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['TrustedProxies']</span></code></a> for
detecting IP address behind proxies.</p>
</dd></dl>

<span class="target" id="servers-allowdeny-rules"></span><dl class="config option">
<dt id="cfg_Servers_AllowDeny_rules">
<code class="sig-name descname">$cfg['Servers'][$i]['AllowDeny']['rules']</code><a class="headerlink" href="#cfg_Servers_AllowDeny_rules" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array of strings</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array()</p>
</dd>
</dl>
<p>The general format for the rules is as such:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>&lt;&#39;allow&#39; | &#39;deny&#39;&gt; &lt;username&gt; [from] &lt;ipmask&gt;
</pre></div>
</div>
<p>If you wish to match all users, it is possible to use a <code class="docutils literal notranslate"><span class="pre">'%'</span></code> as a
wildcard in the <em>username</em> field.</p>
<p>There are a few shortcuts you can
use in the <em>ipmask</em> field as well (please note that those containing
SERVER_ADDRESS might not be available on all webservers):</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>&#39;all&#39; -&gt; 0.0.0.0/0
&#39;localhost&#39; -&gt; 127.0.0.1/8
&#39;localnetA&#39; -&gt; SERVER_ADDRESS/8
&#39;localnetB&#39; -&gt; SERVER_ADDRESS/16
&#39;localnetC&#39; -&gt; SERVER_ADDRESS/24
</pre></div>
</div>
<p>Having an empty rule list is equivalent to either using <code class="docutils literal notranslate"><span class="pre">'allow</span> <span class="pre">%</span>
<span class="pre">from</span> <span class="pre">all'</span></code> if your rule order is set to <code class="docutils literal notranslate"><span class="pre">'deny,allow'</span></code> or <code class="docutils literal notranslate"><span class="pre">'deny</span> <span class="pre">%</span>
<span class="pre">from</span> <span class="pre">all'</span></code> if your rule order is set to <code class="docutils literal notranslate"><span class="pre">'allow,deny'</span></code> or
<code class="docutils literal notranslate"><span class="pre">'explicit'</span></code>.</p>
<p>For the <a class="reference internal" href="glossary.html#term-IP-Address"><span class="xref std std-term">IP Address</span></a> matching
system, the following work:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">xxx.xxx.xxx.xxx</span></code> (an exact <a class="reference internal" href="glossary.html#term-IP-Address"><span class="xref std std-term">IP Address</span></a>)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">xxx.xxx.xxx.[yyy-zzz]</span></code> (an <a class="reference internal" href="glossary.html#term-IP-Address"><span class="xref std std-term">IP Address</span></a> range)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">xxx.xxx.xxx.xxx/nn</span></code> (CIDR, Classless Inter-Domain Routing type <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> addresses)</p></li>
</ul>
<p>But the following does not work:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">xxx.xxx.xxx.xx[yyy-zzz]</span></code> (partial <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> address range)</p></li>
</ul>
<p>For <a class="reference internal" href="glossary.html#term-IPv6"><span class="xref std std-term">IPv6</span></a> addresses, the following work:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx</span></code> (an exact <a class="reference internal" href="glossary.html#term-IPv6"><span class="xref std std-term">IPv6</span></a> address)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:[yyyy-zzzz]</span></code> (an <a class="reference internal" href="glossary.html#term-IPv6"><span class="xref std std-term">IPv6</span></a> address range)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">xxxx:xxxx:xxxx:xxxx/nn</span></code> (CIDR, Classless Inter-Domain Routing type <a class="reference internal" href="glossary.html#term-IPv6"><span class="xref std std-term">IPv6</span></a> addresses)</p></li>
</ul>
<p>But the following does not work:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xx[yyy-zzz]</span></code> (partial <a class="reference internal" href="glossary.html#term-IPv6"><span class="xref std std-term">IPv6</span></a> address range)</p></li>
</ul>
<p>Examples:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;] = &#39;allow,deny&#39;;
$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;] = [&#39;allow bob from all&#39;];
// Allow only &#39;bob&#39; to connect from any host

$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;] = &#39;allow,deny&#39;;
$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;] = [&#39;allow mary from 192.168.100.[50-100]&#39;];
// Allow only &#39;mary&#39; to connect from host ************** through ***************

$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;] = &#39;allow,deny&#39;;
$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;] = [&#39;allow % from 192.168.[5-6].10&#39;];
// Allow any user to connect from host ************ or ************

$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;] = &#39;allow,deny&#39;;
$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;] = [&#39;allow root from ************&#39;,&#39;allow % from ************&#39;];
// Allow any user to connect from ************, and additionally allow root to connect from ************
</pre></div>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_DisableIS">
<code class="sig-name descname">$cfg['Servers'][$i]['DisableIS']</code><a class="headerlink" href="#cfg_Servers_DisableIS" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Disable using <code class="docutils literal notranslate"><span class="pre">INFORMATION_SCHEMA</span></code> to retrieve information (use
<code class="docutils literal notranslate"><span class="pre">SHOW</span></code> commands instead), because of speed issues when many
databases are present.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Enabling this option might give you a big performance boost on older
MySQL servers.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_SignonScript">
<code class="sig-name descname">$cfg['Servers'][$i]['SignonScript']</code><a class="headerlink" href="#cfg_Servers_SignonScript" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.0.</span></p>
</div>
<p>Name of PHP script to be sourced and executed to obtain login
credentials. This is alternative approach to session based single
signon. The script has to provide a function called
<code class="docutils literal notranslate"><span class="pre">get_login_credentials</span></code> which returns list of username and
password, accepting single parameter of existing username (can be
empty). See <code class="file docutils literal notranslate"><span class="pre">examples/signon-script.php</span></code> for an example:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;?</span><span class="nx">php</span>
<span class="sd">/**</span>
<span class="sd"> * Single signon for phpMyAdmin</span>
<span class="sd"> *</span>
<span class="sd"> * This is just example how to use script based single signon with</span>
<span class="sd"> * phpMyAdmin, it is not intended to be perfect code and look, only</span>
<span class="sd"> * shows how you can integrate this functionality in your application.</span>
<span class="sd"> */</span>

<span class="k">declare</span><span class="p">(</span><span class="nx">strict_types</span><span class="o">=</span><span class="mi">1</span><span class="p">);</span>

<span class="c1">// phpcs:disable Squiz.Functions.GlobalFunction</span>

<span class="sd">/**</span>
<span class="sd"> * This function returns username and password.</span>
<span class="sd"> *</span>
<span class="sd"> * It can optionally use configured username as parameter.</span>
<span class="sd"> *</span>
<span class="sd"> * @param string $user User name</span>
<span class="sd"> *</span>
<span class="sd"> * @return array</span>
<span class="sd"> */</span>
<span class="k">function</span> <span class="nf">get_login_credentials</span><span class="p">(</span><span class="nv">$user</span><span class="p">)</span>
<span class="p">{</span>
    <span class="cm">/* Optionally we can use passed username */</span>
    <span class="k">if</span> <span class="p">(</span><span class="o">!</span> <span class="k">empty</span><span class="p">(</span><span class="nv">$user</span><span class="p">))</span> <span class="p">{</span>
        <span class="k">return</span> <span class="p">[</span>
            <span class="nv">$user</span><span class="p">,</span>
            <span class="s1">&#39;password&#39;</span><span class="p">,</span>
        <span class="p">];</span>
    <span class="p">}</span>

    <span class="cm">/* Here we would retrieve the credentials */</span>
    <span class="k">return</span> <span class="p">[</span>
        <span class="s1">&#39;root&#39;</span><span class="p">,</span>
        <span class="s1">&#39;&#39;</span><span class="p">,</span>
    <span class="p">];</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#auth-signon"><span class="std std-ref">Signon authentication mode</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_SignonSession">
<code class="sig-name descname">$cfg['Servers'][$i]['SignonSession']</code><a class="headerlink" href="#cfg_Servers_SignonSession" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Name of session which will be used for signon authentication method.
You should use something different than <code class="docutils literal notranslate"><span class="pre">phpMyAdmin</span></code>, because this
is session which phpMyAdmin uses internally. Takes effect only if
<span class="target" id="index-131"></span><a class="reference internal" href="#cfg_Servers_SignonScript"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['SignonScript']</span></code></a> is not configured.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#auth-signon"><span class="std std-ref">Signon authentication mode</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_SignonCookieParams">
<code class="sig-name descname">$cfg['Servers'][$i]['SignonCookieParams']</code><a class="headerlink" href="#cfg_Servers_SignonCookieParams" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">array()</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.7.0.</span></p>
</div>
<p>An associative array of session cookie parameters of other authentication system.
It is not needed if the other system doesn’t use session_set_cookie_params().
Keys should include ‘lifetime’, ‘path’, ‘domain’, ‘secure’ or ‘httponly’.
Valid values are mentioned in <a class="reference external" href="https://www.php.net/manual/en/function.session-get-cookie-params.php">session_get_cookie_params</a>, they should be set to same values as the
other application uses. Takes effect only if
<span class="target" id="index-132"></span><a class="reference internal" href="#cfg_Servers_SignonScript"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['SignonScript']</span></code></a> is not configured.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#auth-signon"><span class="std std-ref">Signon authentication mode</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_SignonURL">
<code class="sig-name descname">$cfg['Servers'][$i]['SignonURL']</code><a class="headerlink" href="#cfg_Servers_SignonURL" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p><a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> where user will be redirected
to log in for signon authentication method. Should be absolute
including protocol.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#auth-signon"><span class="std std-ref">Signon authentication mode</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_LogoutURL">
<code class="sig-name descname">$cfg['Servers'][$i]['LogoutURL']</code><a class="headerlink" href="#cfg_Servers_LogoutURL" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p><a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> where user will be redirected
after logout (doesn’t affect config authentication method). Should be
absolute including protocol.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Servers_hide_connection_errors">
<code class="sig-name descname">$cfg['Servers'][$i]['hide_connection_errors']</code><a class="headerlink" href="#cfg_Servers_hide_connection_errors" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.9.8.</span></p>
</div>
<p>Whether to show or hide detailed MySQL/MariaDB connection errors on the login page.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This error message can contain the target database server hostname or IP address,
which may reveal information about your network to an attacker.</p>
</div>
</dd></dl>

</div>
<div class="section" id="generic-settings">
<h2>Generic settings<a class="headerlink" href="#generic-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_DisableShortcutKeys">
<code class="sig-name descname">$cfg['DisableShortcutKeys']</code><a class="headerlink" href="#cfg_DisableShortcutKeys" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>You can disable phpMyAdmin shortcut keys by setting <span class="target" id="index-133"></span><a class="reference internal" href="#cfg_DisableShortcutKeys"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['DisableShortcutKeys']</span></code></a> to false.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ServerDefault">
<code class="sig-name descname">$cfg['ServerDefault']</code><a class="headerlink" href="#cfg_ServerDefault" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>1</p>
</dd>
</dl>
<p>If you have more than one server configured, you can set
<span class="target" id="index-134"></span><a class="reference internal" href="#cfg_ServerDefault"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['ServerDefault']</span></code></a> to any one of them to autoconnect to that
server when phpMyAdmin is started, or set it to 0 to be given a list
of servers without logging in.</p>
<p>If you have only one server configured,
<span class="target" id="index-135"></span><a class="reference internal" href="#cfg_ServerDefault"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['ServerDefault']</span></code></a> MUST be set to that server.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_VersionCheck">
<code class="sig-name descname">$cfg['VersionCheck']</code><a class="headerlink" href="#cfg_VersionCheck" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Enables check for latest versions using JavaScript on the main phpMyAdmin
page or by directly accessing <cite>index.php?route=/version-check</cite>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This setting can be adjusted by your vendor.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_ProxyUrl">
<code class="sig-name descname">$cfg['ProxyUrl']</code><a class="headerlink" href="#cfg_ProxyUrl" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The url of the proxy to be used when phpmyadmin needs to access the outside
internet such as when retrieving the latest version info or submitting error
reports.  You need this if the server where phpMyAdmin is installed does not
have direct access to the internet.
The format is: “hostname:portnumber”</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ProxyUser">
<code class="sig-name descname">$cfg['ProxyUser']</code><a class="headerlink" href="#cfg_ProxyUser" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The username for authenticating with the proxy. By default, no
authentication is performed. If a username is supplied, Basic
Authentication will be performed. No other types of authentication
are currently supported.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ProxyPass">
<code class="sig-name descname">$cfg['ProxyPass']</code><a class="headerlink" href="#cfg_ProxyPass" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The password for authenticating with the proxy.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxDbList">
<code class="sig-name descname">$cfg['MaxDbList']</code><a class="headerlink" href="#cfg_MaxDbList" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>100</p>
</dd>
</dl>
<p>The maximum number of database names to be displayed in the main panel’s
database list.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxTableList">
<code class="sig-name descname">$cfg['MaxTableList']</code><a class="headerlink" href="#cfg_MaxTableList" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>250</p>
</dd>
</dl>
<p>The maximum number of table names to be displayed in the main panel’s
list (except on the Export page).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowHint">
<code class="sig-name descname">$cfg['ShowHint']</code><a class="headerlink" href="#cfg_ShowHint" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether or not to show hints (for example, hints when hovering over
table headers).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxCharactersInDisplayedSQL">
<code class="sig-name descname">$cfg['MaxCharactersInDisplayedSQL']</code><a class="headerlink" href="#cfg_MaxCharactersInDisplayedSQL" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>1000</p>
</dd>
</dl>
<p>The maximum number of characters when a <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> query is displayed. The
default limit of 1000 should be correct to avoid the display of tons of
hexadecimal codes that represent BLOBs, but some users have real
<a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> queries that are longer than 1000 characters. Also, if a
query’s length exceeds this limit, this query is not saved in the history.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_PersistentConnections">
<code class="sig-name descname">$cfg['PersistentConnections']</code><a class="headerlink" href="#cfg_PersistentConnections" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Whether <a class="reference external" href="https://www.php.net/manual/en/features.persistent-connections.php">persistent connections</a> should be used or not.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://www.php.net/manual/en/mysqli.persistconns.php">mysqli documentation for persistent connections</a></p>
</div>
<dl class="config option">
<dt id="cfg_ForceSSL">
<code class="sig-name descname">$cfg['ForceSSL']</code><a class="headerlink" href="#cfg_ForceSSL" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 4.6.0: </span>This setting is no longer available since phpMyAdmin 4.6.0. Please
adjust your webserver instead.</p>
</div>
<p>Whether to force using https while accessing phpMyAdmin. In a reverse
proxy setup, setting this to <code class="docutils literal notranslate"><span class="pre">true</span></code> is not supported.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In some setups (like separate SSL proxy or load balancer) you might
have to set <span class="target" id="index-136"></span><a class="reference internal" href="#cfg_PmaAbsoluteUri"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['PmaAbsoluteUri']</span></code></a> for correct
redirection.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_MysqlSslWarningSafeHosts">
<code class="sig-name descname">$cfg['MysqlSslWarningSafeHosts']</code><a class="headerlink" href="#cfg_MysqlSslWarningSafeHosts" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">['127.0.0.1',</span> <span class="pre">'localhost']</span></code></p>
</dd>
</dl>
<p>This search is case-sensitive and will match the exact string only.
If your setup does not use SSL but is safe because you are using a
local connection or private network, you can add your hostname or <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> to the list.
You can also remove the default entries to only include yours.</p>
<p>This check uses the value of <span class="target" id="index-137"></span><a class="reference internal" href="#cfg_Servers_host"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['host']</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.1.0.</span></p>
</div>
<p>Example configuration</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;MysqlSslWarningSafeHosts&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;127.0.0.1&#39;</span><span class="p">,</span> <span class="s1">&#39;localhost&#39;</span><span class="p">,</span> <span class="s1">&#39;mariadb.local&#39;</span><span class="p">];</span>
</pre></div>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_ExecTimeLimit">
<code class="sig-name descname">$cfg['ExecTimeLimit']</code><a class="headerlink" href="#cfg_ExecTimeLimit" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer [number of seconds]</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>300</p>
</dd>
</dl>
<p>Set the number of seconds a script is allowed to run. If seconds is
set to zero, no time limit is imposed. This setting is used while
importing/exporting dump files but has
no effect when PHP is running in safe mode.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SessionSavePath">
<code class="sig-name descname">$cfg['SessionSavePath']</code><a class="headerlink" href="#cfg_SessionSavePath" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Path for storing session data (<a class="reference external" href="https://www.php.net/session_save_path">session_save_path PHP parameter</a>).</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This folder should not be publicly accessible through the webserver,
otherwise you risk leaking private data from your session.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_MemoryLimit">
<code class="sig-name descname">$cfg['MemoryLimit']</code><a class="headerlink" href="#cfg_MemoryLimit" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string [number of bytes]</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'-1'</span></code></p>
</dd>
</dl>
<p>Set the number of bytes a script is allowed to allocate. If set to
<code class="docutils literal notranslate"><span class="pre">'-1'</span></code>, no limit is imposed. If set to <code class="docutils literal notranslate"><span class="pre">'0'</span></code>, no change of the
memory limit is attempted and the <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> <code class="docutils literal notranslate"><span class="pre">memory_limit</span></code> is
used.</p>
<p>This setting is used while importing/exporting dump files
so you definitely don’t want to put here a too low
value. It has no effect when PHP is running in safe mode.</p>
<p>You can also use any string as in <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>, eg. ‘16M’. Ensure you
don’t omit the suffix (16 means 16 bytes!)</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SkipLockedTables">
<code class="sig-name descname">$cfg['SkipLockedTables']</code><a class="headerlink" href="#cfg_SkipLockedTables" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Mark used tables and make it possible to show databases with locked
tables (since MySQL 3.23.30).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowSQL">
<code class="sig-name descname">$cfg['ShowSQL']</code><a class="headerlink" href="#cfg_ShowSQL" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> queries
generated by phpMyAdmin should be displayed or not.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_RetainQueryBox">
<code class="sig-name descname">$cfg['RetainQueryBox']</code><a class="headerlink" href="#cfg_RetainQueryBox" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether the <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> query box
should be kept displayed after its submission.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_CodemirrorEnable">
<code class="sig-name descname">$cfg['CodemirrorEnable']</code><a class="headerlink" href="#cfg_CodemirrorEnable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to use a Javascript code editor for SQL query boxes.
CodeMirror provides syntax highlighting and line numbers.  However,
middle-clicking for pasting the clipboard contents in some Linux
distributions (such as Ubuntu) is not supported by all browsers.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultForeignKeyChecks">
<code class="sig-name descname">$cfg['DefaultForeignKeyChecks']</code><a class="headerlink" href="#cfg_DefaultForeignKeyChecks" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'default'</span></code></p>
</dd>
</dl>
<p>Default value of the checkbox for foreign key checks, to disable/enable
foreign key checks for certain queries. The possible values are <code class="docutils literal notranslate"><span class="pre">'default'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'enable'</span></code> or <code class="docutils literal notranslate"><span class="pre">'disable'</span></code>. If set to <code class="docutils literal notranslate"><span class="pre">'default'</span></code>, the value of the
MySQL variable <code class="docutils literal notranslate"><span class="pre">FOREIGN_KEY_CHECKS</span></code> is used.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_AllowUserDropDatabase">
<code class="sig-name descname">$cfg['AllowUserDropDatabase']</code><a class="headerlink" href="#cfg_AllowUserDropDatabase" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This is not a security measure as there will be always ways to
circumvent this. If you want to prohibit users from dropping databases,
revoke their corresponding DROP privilege.</p>
</div>
<p>Defines whether normal users (non-administrator) are allowed to delete
their own database or not. If set as false, the link <span class="guilabel">Drop
Database</span> will not be shown, and even a <code class="docutils literal notranslate"><span class="pre">DROP</span> <span class="pre">DATABASE</span> <span class="pre">mydatabase</span></code> will
be rejected. Quite practical for <a class="reference internal" href="glossary.html#term-ISP"><span class="xref std std-term">ISP</span></a> ‘s with many customers.</p>
<p>This limitation of <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> queries is not as strict as when using MySQL
privileges. This is due to nature of <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> queries which might be
quite complicated.  So this choice should be viewed as help to avoid
accidental dropping rather than strict privilege limitation.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Confirm">
<code class="sig-name descname">$cfg['Confirm']</code><a class="headerlink" href="#cfg_Confirm" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether a warning (“Are your really sure…”) should be displayed when
you’re about to lose data.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_UseDbSearch">
<code class="sig-name descname">$cfg['UseDbSearch']</code><a class="headerlink" href="#cfg_UseDbSearch" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Define whether the “search string inside database” is enabled or not.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_IgnoreMultiSubmitErrors">
<code class="sig-name descname">$cfg['IgnoreMultiSubmitErrors']</code><a class="headerlink" href="#cfg_IgnoreMultiSubmitErrors" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Define whether phpMyAdmin will continue executing a multi-query
statement if one of the queries fails. Default is to abort execution.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_enable_drag_drop_import">
<code class="sig-name descname">$cfg['enable_drag_drop_import']</code><a class="headerlink" href="#cfg_enable_drag_drop_import" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether or not the drag and drop import feature is enabled.
When enabled, a user can drag a file in to their browser and phpMyAdmin will
attempt to import the file.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_URLQueryEncryption">
<code class="sig-name descname">$cfg['URLQueryEncryption']</code><a class="headerlink" href="#cfg_URLQueryEncryption" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.9.8.</span></p>
</div>
<p>Define whether phpMyAdmin will encrypt sensitive data (like database name
and table name) from the URL query string. Default is to not encrypt the URL
query string.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_URLQueryEncryptionSecretKey">
<code class="sig-name descname">$cfg['URLQueryEncryptionSecretKey']</code><a class="headerlink" href="#cfg_URLQueryEncryptionSecretKey" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.9.8.</span></p>
</div>
<p>A secret key used to encrypt/decrypt the URL query string.
Should be 32 bytes long.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faq2-10"><span class="std std-ref">2.10 How to generate a string of random bytes</span></a></p>
</div>
</dd></dl>

</div>
<div class="section" id="cookie-authentication-options">
<h2>Cookie authentication options<a class="headerlink" href="#cookie-authentication-options" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_blowfish_secret">
<code class="sig-name descname">$cfg['blowfish_secret']</code><a class="headerlink" href="#cfg_blowfish_secret" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The “cookie” auth_type uses the <a class="reference internal" href="glossary.html#term-Sodium"><span class="xref std std-term">Sodium</span></a> extension to encrypt the cookies (see <a class="reference internal" href="glossary.html#term-Cookie"><span class="xref std std-term">Cookie</span></a>). If you are
using the “cookie” auth_type, enter here a generated string of random bytes to be used as an encryption key. It
will be used internally by the <a class="reference internal" href="glossary.html#term-Sodium"><span class="xref std std-term">Sodium</span></a> extension: you won’t be prompted for this encryption key.</p>
<p>Since a binary string is usually not printable, it can be converted into a hexadecimal representation (using a
function like <a class="reference external" href="https://www.php.net/sodium_bin2hex">sodium_bin2hex</a>) and then used in the configuration file. For
example:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="c1">// The string is a hexadecimal representation of a 32-bytes long string of random bytes.</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;blowfish_secret&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="nx">sodium_hex2bin</span><span class="p">(</span><span class="s1">&#39;f16ce59f45714194371b48fe362072dc3b019da7861558cd4ad29e4d6fb13851&#39;</span><span class="p">);</span>
</pre></div>
</div>
<p>Using a binary string is recommended. However, if all 32 bytes of the string are visible
characters, then a function like <a class="reference external" href="https://www.php.net/sodium_bin2hex">sodium_bin2hex</a> is not required. For
example:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="c1">// A string of 32 characters.</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;blowfish_secret&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;JOFw435365IScA&amp;Q!cDugr!lSfuAz*OW&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The encryption key must be 32 bytes long. If it is longer than the length of bytes, only the first 32 bytes will
be used, and if it is shorter, a new temporary key will be automatically generated for you. However, this
temporary key will only last for the duration of the session.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The configuration is called blowfish_secret for historical reasons as
Blowfish algorithm was originally used to do the encryption.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1.0: </span>Since version 3.1.0 phpMyAdmin can generate this on the fly, but it
makes a bit weaker security as this generated secret is stored in
session and furthermore it makes impossible to recall user name from
cookie.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 5.2.0: </span>Since version 5.2.0, phpMyAdmin uses the
<a class="reference external" href="https://www.php.net/sodium_crypto_secretbox">sodium_crypto_secretbox</a> and
<a class="reference external" href="https://www.php.net/sodium_crypto_secretbox_open">sodium_crypto_secretbox_open</a> PHP functions to encrypt
and decrypt cookies, respectively.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faq2-10"><span class="std std-ref">2.10 How to generate a string of random bytes</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_CookieSameSite">
<code class="sig-name descname">$cfg['CookieSameSite']</code><a class="headerlink" href="#cfg_CookieSameSite" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'Strict'</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.1.0.</span></p>
</div>
<p>It sets SameSite attribute of the Set-Cookie <a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> response header.
Valid values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">Lax</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Strict</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://tools.ietf.org/id/draft-ietf-httpbis-rfc6265bis-03.html#rfc.section.5.3.7">rfc6265 bis</a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_LoginCookieRecall">
<code class="sig-name descname">$cfg['LoginCookieRecall']</code><a class="headerlink" href="#cfg_LoginCookieRecall" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Define whether the previous login should be recalled or not in cookie
authentication mode.</p>
<p>This is automatically disabled if you do not have
configured <span class="target" id="index-138"></span><a class="reference internal" href="#cfg_blowfish_secret"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['blowfish_secret']</span></code></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_LoginCookieValidity">
<code class="sig-name descname">$cfg['LoginCookieValidity']</code><a class="headerlink" href="#cfg_LoginCookieValidity" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer [number of seconds]</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>1440</p>
</dd>
</dl>
<p>Define how long a login cookie is valid. Please note that php
configuration option <a class="reference external" href="https://www.php.net/manual/en/session.configuration.php#ini.session.gc-maxlifetime">session.gc_maxlifetime</a> might limit session validity and if the session is lost,
the login cookie is also invalidated. So it is a good idea to set
<code class="docutils literal notranslate"><span class="pre">session.gc_maxlifetime</span></code> at least to the same value of
<span class="target" id="index-139"></span><a class="reference internal" href="#cfg_LoginCookieValidity"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['LoginCookieValidity']</span></code></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_LoginCookieStore">
<code class="sig-name descname">$cfg['LoginCookieStore']</code><a class="headerlink" href="#cfg_LoginCookieStore" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer [number of seconds]</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>0</p>
</dd>
</dl>
<p>Define how long login cookie should be stored in browser. Default 0
means that it will be kept for existing session. This is recommended
for not trusted environments.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_LoginCookieDeleteAll">
<code class="sig-name descname">$cfg['LoginCookieDeleteAll']</code><a class="headerlink" href="#cfg_LoginCookieDeleteAll" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>If enabled (default), logout deletes cookies for all servers,
otherwise only for current one. Setting this to false makes it easy to
forget to log out from other server, when you are using more of them.</p>
</dd></dl>

<span class="target" id="allowarbitraryserver"></span><dl class="config option">
<dt id="cfg_AllowArbitraryServer">
<code class="sig-name descname">$cfg['AllowArbitraryServer']</code><a class="headerlink" href="#cfg_AllowArbitraryServer" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>If enabled, allows you to log in to arbitrary servers using cookie
authentication.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Please use this carefully, as this may allow users access to MySQL servers
behind the firewall where your <a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> server is placed.
See also <span class="target" id="index-140"></span><a class="reference internal" href="#cfg_ArbitraryServerRegexp"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['ArbitraryServerRegexp']</span></code></a>.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_ArbitraryServerRegexp">
<code class="sig-name descname">$cfg['ArbitraryServerRegexp']</code><a class="headerlink" href="#cfg_ArbitraryServerRegexp" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Restricts the MySQL servers to which the user can log in when
<span class="target" id="index-141"></span><a class="reference internal" href="#cfg_AllowArbitraryServer"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['AllowArbitraryServer']</span></code></a> is enabled by
matching the <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> or the hostname of the MySQL server
to the given regular expression. The regular expression must be enclosed
with a delimiter character.</p>
<p>It is recommended to include start and end symbols in the regular
expression, so that you can avoid partial matches on the string.</p>
<p><strong>Examples:</strong></p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="c1">// Allow connection to three listed servers:</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;ArbitraryServerRegexp&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/^(server|another|yetdifferent)$/&#39;</span><span class="p">;</span>

<span class="c1">// Allow connection to range of IP addresses:</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;ArbitraryServerRegexp&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;@^192\.168\.0\.[0-9]{1,}$@&#39;</span><span class="p">;</span>

<span class="c1">// Allow connection to server name ending with -mysql:</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;ArbitraryServerRegexp&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;@^[^:]\-mysql$@&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The whole server name is matched, it can include port as well. Due to
way MySQL is permissive in connection parameters, it is possible to use
connection strings as <code class="docutils literal notranslate"><span class="pre">`server:3306-mysql`</span></code>. This can be used to
bypass regular expression by the suffix, while connecting to another
server.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaMethod">
<code class="sig-name descname">$cfg['CaptchaMethod']</code><a class="headerlink" href="#cfg_CaptchaMethod" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'invisible'</span></code></p>
</dd>
</dl>
<p>Valid values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">'invisible'</span></code> Use an invisible captcha checking method;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'checkbox'</span></code> Use a checkbox to confirm the user is not a robot.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.0.3.</span></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaApi">
<code class="sig-name descname">$cfg['CaptchaApi']</code><a class="headerlink" href="#cfg_CaptchaApi" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'https://www.google.com/recaptcha/api.js'</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.1.0.</span></p>
</div>
<p>The URL for the reCaptcha v2 service’s API, either Google’s or a compatible one.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaCsp">
<code class="sig-name descname">$cfg['CaptchaCsp']</code><a class="headerlink" href="#cfg_CaptchaCsp" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'https://apis.google.com</span> <span class="pre">https://www.google.com/recaptcha/</span> <span class="pre">https://www.gstatic.com/recaptcha/</span> <span class="pre">https://ssl.gstatic.com/'</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.1.0.</span></p>
</div>
<p>The Content-Security-Policy snippet (URLs from which to allow embedded content)
for the reCaptcha v2 service, either Google’s or a compatible one.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaRequestParam">
<code class="sig-name descname">$cfg['CaptchaRequestParam']</code><a class="headerlink" href="#cfg_CaptchaRequestParam" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'g-recaptcha'</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.1.0.</span></p>
</div>
<p>The request parameter used for the reCaptcha v2 service.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaResponseParam">
<code class="sig-name descname">$cfg['CaptchaResponseParam']</code><a class="headerlink" href="#cfg_CaptchaResponseParam" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'g-recaptcha-response'</span></code></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.1.0.</span></p>
</div>
<p>The response parameter used for the reCaptcha v2 service.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaLoginPublicKey">
<code class="sig-name descname">$cfg['CaptchaLoginPublicKey']</code><a class="headerlink" href="#cfg_CaptchaLoginPublicKey" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The public key for the reCaptcha service that can be obtained from the
“Admin Console” on <a class="reference external" href="https://www.google.com/recaptcha/about/">https://www.google.com/recaptcha/about/</a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://developers.google.com/recaptcha/docs/v3">https://developers.google.com/recaptcha/docs/v3</a>&gt;</p>
</div>
<p>reCaptcha will be then used in <a class="reference internal" href="setup.html#cookie"><span class="std std-ref">Cookie authentication mode</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.1.0.</span></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaLoginPrivateKey">
<code class="sig-name descname">$cfg['CaptchaLoginPrivateKey']</code><a class="headerlink" href="#cfg_CaptchaLoginPrivateKey" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The private key for the reCaptcha service that can be obtained from the
“Admin Console” on <a class="reference external" href="https://www.google.com/recaptcha/about/">https://www.google.com/recaptcha/about/</a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://developers.google.com/recaptcha/docs/v3">https://developers.google.com/recaptcha/docs/v3</a>&gt;</p>
</div>
<p>reCaptcha will be then used in <a class="reference internal" href="setup.html#cookie"><span class="std std-ref">Cookie authentication mode</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.1.0.</span></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_CaptchaSiteVerifyURL">
<code class="sig-name descname">$cfg['CaptchaSiteVerifyURL']</code><a class="headerlink" href="#cfg_CaptchaSiteVerifyURL" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The URL for the reCaptcha service to do siteverify action.</p>
<p>reCaptcha will be then used in <a class="reference internal" href="setup.html#cookie"><span class="std std-ref">Cookie authentication mode</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.1.0.</span></p>
</div>
</dd></dl>

</div>
<div class="section" id="navigation-panel-setup">
<h2>Navigation panel setup<a class="headerlink" href="#navigation-panel-setup" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_ShowDatabasesNavigationAsTree">
<code class="sig-name descname">$cfg['ShowDatabasesNavigationAsTree']</code><a class="headerlink" href="#cfg_ShowDatabasesNavigationAsTree" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>In the navigation panel, replaces the database tree with a selector</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_FirstLevelNavigationItems">
<code class="sig-name descname">$cfg['FirstLevelNavigationItems']</code><a class="headerlink" href="#cfg_FirstLevelNavigationItems" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>100</p>
</dd>
</dl>
<p>The number of first level databases that can be displayed on each page
of navigation tree.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxNavigationItems">
<code class="sig-name descname">$cfg['MaxNavigationItems']</code><a class="headerlink" href="#cfg_MaxNavigationItems" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>50</p>
</dd>
</dl>
<p>The number of items (tables, columns, indexes) that can be displayed on each
page of the navigation tree.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeEnableGrouping">
<code class="sig-name descname">$cfg['NavigationTreeEnableGrouping']</code><a class="headerlink" href="#cfg_NavigationTreeEnableGrouping" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to group the databases based on a common prefix
in their name <span class="target" id="index-142"></span><a class="reference internal" href="#cfg_NavigationTreeDbSeparator"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NavigationTreeDbSeparator']</span></code></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeDbSeparator">
<code class="sig-name descname">$cfg['NavigationTreeDbSeparator']</code><a class="headerlink" href="#cfg_NavigationTreeDbSeparator" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'_'</span></code></p>
</dd>
</dl>
<p>The string used to separate the parts of the database name when
showing them in a tree.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeTableSeparator">
<code class="sig-name descname">$cfg['NavigationTreeTableSeparator']</code><a class="headerlink" href="#cfg_NavigationTreeTableSeparator" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string or array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'__'</span></code></p>
</dd>
</dl>
<p>Defines a string to be used to nest table spaces. This means if you have
tables like <code class="docutils literal notranslate"><span class="pre">first__second__third</span></code> this will be shown as a three-level
hierarchy like: first &gt; second &gt; third.  If set to false or empty, the
feature is disabled. NOTE: You should not use this separator at the
beginning or end of a table name or multiple times after another without
any other characters in between.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeTableLevel">
<code class="sig-name descname">$cfg['NavigationTreeTableLevel']</code><a class="headerlink" href="#cfg_NavigationTreeTableLevel" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>1</p>
</dd>
</dl>
<p>Defines how many sublevels should be displayed when splitting up
tables by the above separator.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NumRecentTables">
<code class="sig-name descname">$cfg['NumRecentTables']</code><a class="headerlink" href="#cfg_NumRecentTables" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>10</p>
</dd>
</dl>
<p>The maximum number of recently used tables shown in the navigation
panel. Set this to 0 (zero) to disable the listing of recent tables.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NumFavoriteTables">
<code class="sig-name descname">$cfg['NumFavoriteTables']</code><a class="headerlink" href="#cfg_NumFavoriteTables" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>10</p>
</dd>
</dl>
<p>The maximum number of favorite tables shown in the navigation
panel. Set this to 0 (zero) to disable the listing of favorite tables.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ZeroConf">
<code class="sig-name descname">$cfg['ZeroConf']</code><a class="headerlink" href="#cfg_ZeroConf" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Enables Zero Configuration mode in which the user will be offered a choice to
create phpMyAdmin configuration storage in the current database
or use the existing one, if already present.</p>
<p>This setting has no effect if the phpMyAdmin configuration storage database
is properly created and the related configuration directives (such as
<span class="target" id="index-143"></span><a class="reference internal" href="#cfg_Servers_pmadb"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['pmadb']</span></code></a> and so on) are configured.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationLinkWithMainPanel">
<code class="sig-name descname">$cfg['NavigationLinkWithMainPanel']</code><a class="headerlink" href="#cfg_NavigationLinkWithMainPanel" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether or not to link with main panel by highlighting
the current database or table.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationDisplayLogo">
<code class="sig-name descname">$cfg['NavigationDisplayLogo']</code><a class="headerlink" href="#cfg_NavigationDisplayLogo" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether or not to display the phpMyAdmin logo at the top of
the navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationLogoLink">
<code class="sig-name descname">$cfg['NavigationLogoLink']</code><a class="headerlink" href="#cfg_NavigationLogoLink" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'index.php'</span></code></p>
</dd>
</dl>
<p>Enter the <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> where the logo in the navigation panel will point to.
For use especially with self made theme which changes this.
For relative/internal URLs, you need to have leading `` ./ `` or trailing characters `` ? `` such as <code class="docutils literal notranslate"><span class="pre">'./index.php?route=/server/sql?'</span></code>.
For external URLs, you should include URL protocol schemes (<code class="docutils literal notranslate"><span class="pre">http</span></code> or <code class="docutils literal notranslate"><span class="pre">https</span></code>) with absolute URLs.</p>
<p>You may want to make the link open in a new browser tab, for that you need to use <span class="target" id="index-144"></span><a class="reference internal" href="#cfg_NavigationLogoLinkWindow"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NavigationLogoLinkWindow']</span></code></a></p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationLogoLinkWindow">
<code class="sig-name descname">$cfg['NavigationLogoLinkWindow']</code><a class="headerlink" href="#cfg_NavigationLogoLinkWindow" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'main'</span></code></p>
</dd>
</dl>
<p>Whether to open the linked page in the main window (<code class="docutils literal notranslate"><span class="pre">main</span></code>) or in a
new one (<code class="docutils literal notranslate"><span class="pre">new</span></code>). Note: use <code class="docutils literal notranslate"><span class="pre">new</span></code> if you are linking to
<code class="docutils literal notranslate"><span class="pre">phpmyadmin.net</span></code>.</p>
<p>To open the link in the main window you will need to add the value of <span class="target" id="index-145"></span><a class="reference internal" href="#cfg_NavigationLogoLink"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NavigationLogoLink']</span></code></a>
to <span class="target" id="index-146"></span><a class="reference internal" href="#cfg_CSPAllow"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['CSPAllow']</span></code></a> because of the <a class="reference internal" href="glossary.html#term-Content-Security-Policy"><span class="xref std std-term">Content Security Policy</span></a> header.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeDisplayItemFilterMinimum">
<code class="sig-name descname">$cfg['NavigationTreeDisplayItemFilterMinimum']</code><a class="headerlink" href="#cfg_NavigationTreeDisplayItemFilterMinimum" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>30</p>
</dd>
</dl>
<p>Defines the minimum number of items (tables, views, routines and
events) to display a JavaScript filter box above the list of items in
the navigation tree.</p>
<p>To disable the filter completely some high number can be used (e.g. 9999)</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeDisplayDbFilterMinimum">
<code class="sig-name descname">$cfg['NavigationTreeDisplayDbFilterMinimum']</code><a class="headerlink" href="#cfg_NavigationTreeDisplayDbFilterMinimum" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>30</p>
</dd>
</dl>
<p>Defines the minimum number of databases to display a JavaScript filter
box above the list of databases in the navigation tree.</p>
<p>To disable the filter completely some high number can be used
(e.g. 9999)</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationDisplayServers">
<code class="sig-name descname">$cfg['NavigationDisplayServers']</code><a class="headerlink" href="#cfg_NavigationDisplayServers" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether or not to display a server choice at the top of the
navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_DisplayServersList">
<code class="sig-name descname">$cfg['DisplayServersList']</code><a class="headerlink" href="#cfg_DisplayServersList" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether to display this server choice as links instead of in a
drop-down.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeDefaultTabTable">
<code class="sig-name descname">$cfg['NavigationTreeDefaultTabTable']</code><a class="headerlink" href="#cfg_NavigationTreeDefaultTabTable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'structure'</span></code></p>
</dd>
</dl>
<p>Defines the tab displayed by default when clicking the small icon next
to each table name in the navigation panel. The possible values are the
localized equivalent of:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">structure</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sql</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">search</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">insert</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">browse</span></code></p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeDefaultTabTable2">
<code class="sig-name descname">$cfg['NavigationTreeDefaultTabTable2']</code><a class="headerlink" href="#cfg_NavigationTreeDefaultTabTable2" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>null</p>
</dd>
</dl>
<p>Defines the tab displayed by default when clicking the second small icon next
to each table name in the navigation panel. The possible values are the
localized equivalent of:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">(empty)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">structure</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sql</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">search</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">insert</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">browse</span></code></p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeEnableExpansion">
<code class="sig-name descname">$cfg['NavigationTreeEnableExpansion']</code><a class="headerlink" href="#cfg_NavigationTreeEnableExpansion" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to offer the possibility of tree expansion in the navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeShowTables">
<code class="sig-name descname">$cfg['NavigationTreeShowTables']</code><a class="headerlink" href="#cfg_NavigationTreeShowTables" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to show tables under database in the navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeShowViews">
<code class="sig-name descname">$cfg['NavigationTreeShowViews']</code><a class="headerlink" href="#cfg_NavigationTreeShowViews" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to show views under database in the navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeShowFunctions">
<code class="sig-name descname">$cfg['NavigationTreeShowFunctions']</code><a class="headerlink" href="#cfg_NavigationTreeShowFunctions" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to show functions under database in the navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeShowProcedures">
<code class="sig-name descname">$cfg['NavigationTreeShowProcedures']</code><a class="headerlink" href="#cfg_NavigationTreeShowProcedures" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to show procedures under database in the navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationTreeShowEvents">
<code class="sig-name descname">$cfg['NavigationTreeShowEvents']</code><a class="headerlink" href="#cfg_NavigationTreeShowEvents" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to show events under database in the navigation panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NavigationWidth">
<code class="sig-name descname">$cfg['NavigationWidth']</code><a class="headerlink" href="#cfg_NavigationWidth" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>240</p>
</dd>
</dl>
<p>Navigation panel width, set to 0 to collapse it by default.</p>
</dd></dl>

</div>
<div class="section" id="main-panel">
<h2>Main panel<a class="headerlink" href="#main-panel" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_ShowStats">
<code class="sig-name descname">$cfg['ShowStats']</code><a class="headerlink" href="#cfg_ShowStats" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether or not to display space usage and statistics about
databases and tables. Note that statistics requires at least MySQL
3.23.3 and that, at this date, MySQL doesn’t return such information
for Berkeley DB tables.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowServerInfo">
<code class="sig-name descname">$cfg['ShowServerInfo']</code><a class="headerlink" href="#cfg_ShowServerInfo" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to display detailed server information on main page.
You can additionally hide more information by using
<span class="target" id="index-147"></span><a class="reference internal" href="#cfg_Servers_verbose"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['verbose']</span></code></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowPhpInfo">
<code class="sig-name descname">$cfg['ShowPhpInfo']</code><a class="headerlink" href="#cfg_ShowPhpInfo" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether to display the <span class="guilabel">PHP information</span> or not at
the starting main (right) frame.</p>
<p>Please note that to block the usage of <code class="docutils literal notranslate"><span class="pre">phpinfo()</span></code> in scripts, you have to
put this in your <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>:</p>
<div class="highlight-ini notranslate"><div class="highlight"><pre><span></span><span class="na">disable_functions</span> <span class="o">=</span> <span class="s">phpinfo()</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Enabling phpinfo page will leak quite a lot of information about server
setup. Is it not recommended to enable this on shared installations.</p>
<p>This might also make easier some remote attacks on your installations,
so enable this only when needed.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowChgPassword">
<code class="sig-name descname">$cfg['ShowChgPassword']</code><a class="headerlink" href="#cfg_ShowChgPassword" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to display the <span class="guilabel">Change password</span> link or not at
the starting main (right) frame. This setting does not check MySQL commands
entered directly.</p>
<p>Please note that enabling the <span class="guilabel">Change password</span> link has no effect
with config authentication mode: because of the hard coded password value
in the configuration file, end users can’t be allowed to change their
passwords.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowCreateDb">
<code class="sig-name descname">$cfg['ShowCreateDb']</code><a class="headerlink" href="#cfg_ShowCreateDb" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to display the form for creating database or not at the
starting main (right) frame. This setting does not check MySQL commands
entered directly.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowGitRevision">
<code class="sig-name descname">$cfg['ShowGitRevision']</code><a class="headerlink" href="#cfg_ShowGitRevision" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to display information about the current Git revision (if
applicable) on the main panel.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MysqlMinVersion">
<code class="sig-name descname">$cfg['MysqlMinVersion']</code><a class="headerlink" href="#cfg_MysqlMinVersion" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
</dl>
<p>Defines the minimum supported MySQL version. The default is chosen
by the phpMyAdmin team; however this directive was asked by a developer
of the Plesk control panel to ease integration with older MySQL servers
(where most of the phpMyAdmin features work).</p>
</dd></dl>

</div>
<div class="section" id="database-structure">
<h2>Database structure<a class="headerlink" href="#database-structure" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_ShowDbStructureCreation">
<code class="sig-name descname">$cfg['ShowDbStructureCreation']</code><a class="headerlink" href="#cfg_ShowDbStructureCreation" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether the database structure page (tables list) has a
“Creation” column that displays when each table was created.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowDbStructureLastUpdate">
<code class="sig-name descname">$cfg['ShowDbStructureLastUpdate']</code><a class="headerlink" href="#cfg_ShowDbStructureLastUpdate" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether the database structure page (tables list) has a “Last
update” column that displays when each table was last updated.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowDbStructureLastCheck">
<code class="sig-name descname">$cfg['ShowDbStructureLastCheck']</code><a class="headerlink" href="#cfg_ShowDbStructureLastCheck" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether the database structure page (tables list) has a “Last
check” column that displays when each table was last checked.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_HideStructureActions">
<code class="sig-name descname">$cfg['HideStructureActions']</code><a class="headerlink" href="#cfg_HideStructureActions" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether the table structure actions are hidden under a “<span class="guilabel">More</span>”
drop-down.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowColumnComments">
<code class="sig-name descname">$cfg['ShowColumnComments']</code><a class="headerlink" href="#cfg_ShowColumnComments" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to show column comments as a column in the table structure view.</p>
</dd></dl>

</div>
<div class="section" id="browse-mode">
<h2>Browse mode<a class="headerlink" href="#browse-mode" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_TableNavigationLinksMode">
<code class="sig-name descname">$cfg['TableNavigationLinksMode']</code><a class="headerlink" href="#cfg_TableNavigationLinksMode" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'icons'</span></code></p>
</dd>
</dl>
<p>Defines whether the table navigation links contain <code class="docutils literal notranslate"><span class="pre">'icons'</span></code>, <code class="docutils literal notranslate"><span class="pre">'text'</span></code>
or <code class="docutils literal notranslate"><span class="pre">'both'</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ActionLinksMode">
<code class="sig-name descname">$cfg['ActionLinksMode']</code><a class="headerlink" href="#cfg_ActionLinksMode" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'both'</span></code></p>
</dd>
</dl>
<p>If set to <code class="docutils literal notranslate"><span class="pre">icons</span></code>, will display icons instead of text for db and table
properties links (like <span class="guilabel">Browse</span>, <span class="guilabel">Select</span>,
<span class="guilabel">Insert</span>, …). Can be set to <code class="docutils literal notranslate"><span class="pre">'both'</span></code>
if you want icons AND text. When set to <code class="docutils literal notranslate"><span class="pre">text</span></code>, will only show text.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_RowActionType">
<code class="sig-name descname">$cfg['RowActionType']</code><a class="headerlink" href="#cfg_RowActionType" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'both'</span></code></p>
</dd>
</dl>
<p>Whether to display icons or text or both icons and text in table row action
segment. Value can be either of <code class="docutils literal notranslate"><span class="pre">'icons'</span></code>, <code class="docutils literal notranslate"><span class="pre">'text'</span></code> or <code class="docutils literal notranslate"><span class="pre">'both'</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowAll">
<code class="sig-name descname">$cfg['ShowAll']</code><a class="headerlink" href="#cfg_ShowAll" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether a user should be displayed a “<span class="guilabel">Show all</span>” button in browse
mode or not in all cases. By default it is shown only on small tables (less
than 500 rows) to avoid performance issues while getting too many rows.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxRows">
<code class="sig-name descname">$cfg['MaxRows']</code><a class="headerlink" href="#cfg_MaxRows" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>25</p>
</dd>
</dl>
<p>Number of rows displayed when browsing a result set and no LIMIT
clause is used. If the result set contains more rows, “<span class="guilabel">Previous</span>” and
“<span class="guilabel">Next</span>” links will be shown. Possible values: 25,50,100,250,500.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Order">
<code class="sig-name descname">$cfg['Order']</code><a class="headerlink" href="#cfg_Order" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'SMART'</span></code></p>
</dd>
</dl>
<p>Defines whether columns are displayed in ascending (<code class="docutils literal notranslate"><span class="pre">ASC</span></code>) order, in
descending (<code class="docutils literal notranslate"><span class="pre">DESC</span></code>) order or in a “smart” (<code class="docutils literal notranslate"><span class="pre">SMART</span></code>) order - I.E.
descending order for columns of type TIME, DATE, DATETIME and
TIMESTAMP, ascending order else- by default.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4.0: </span>Since phpMyAdmin 3.4.0 the default value is <code class="docutils literal notranslate"><span class="pre">'SMART'</span></code>.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_GridEditing">
<code class="sig-name descname">$cfg['GridEditing']</code><a class="headerlink" href="#cfg_GridEditing" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'double-click'</span></code></p>
</dd>
</dl>
<p>Defines which action (<code class="docutils literal notranslate"><span class="pre">double-click</span></code> or <code class="docutils literal notranslate"><span class="pre">click</span></code>) triggers grid
editing. Can be deactivated with the <code class="docutils literal notranslate"><span class="pre">disabled</span></code> value.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_RelationalDisplay">
<code class="sig-name descname">$cfg['RelationalDisplay']</code><a class="headerlink" href="#cfg_RelationalDisplay" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'K'</span></code></p>
</dd>
</dl>
<p>Defines the initial behavior for Options &gt; Relational. <code class="docutils literal notranslate"><span class="pre">K</span></code>, which
is the default, displays the key while <code class="docutils literal notranslate"><span class="pre">D</span></code> shows the display column.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SaveCellsAtOnce">
<code class="sig-name descname">$cfg['SaveCellsAtOnce']</code><a class="headerlink" href="#cfg_SaveCellsAtOnce" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether or not to save all edited cells at once for grid
editing.</p>
</dd></dl>

</div>
<div class="section" id="editing-mode">
<h2>Editing mode<a class="headerlink" href="#editing-mode" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_ProtectBinary">
<code class="sig-name descname">$cfg['ProtectBinary']</code><a class="headerlink" href="#cfg_ProtectBinary" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean or string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'blob'</span></code></p>
</dd>
</dl>
<p>Defines whether <code class="docutils literal notranslate"><span class="pre">BLOB</span></code> or <code class="docutils literal notranslate"><span class="pre">BINARY</span></code> columns are protected from
editing when browsing a table’s content. Valid values are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">false</span></code> to allow editing of all columns;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'blob'</span></code> to allow editing of all columns except <code class="docutils literal notranslate"><span class="pre">BLOBS</span></code>;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'noblob'</span></code> to disallow editing of all columns except <code class="docutils literal notranslate"><span class="pre">BLOBS</span></code> (the
opposite of <code class="docutils literal notranslate"><span class="pre">'blob'</span></code>);</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'all'</span></code> to disallow editing of all <code class="docutils literal notranslate"><span class="pre">BINARY</span></code> or <code class="docutils literal notranslate"><span class="pre">BLOB</span></code> columns.</p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowFunctionFields">
<code class="sig-name descname">$cfg['ShowFunctionFields']</code><a class="headerlink" href="#cfg_ShowFunctionFields" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether or not MySQL functions fields should be initially
displayed in edit/insert mode. Since version 2.10, the user can toggle
this setting from the interface.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowFieldTypesInDataEditView">
<code class="sig-name descname">$cfg['ShowFieldTypesInDataEditView']</code><a class="headerlink" href="#cfg_ShowFieldTypesInDataEditView" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether or not type fields should be initially displayed in
edit/insert mode. The user can toggle this setting from the interface.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_InsertRows">
<code class="sig-name descname">$cfg['InsertRows']</code><a class="headerlink" href="#cfg_InsertRows" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>2</p>
</dd>
</dl>
<p>Defines the default number of rows to be entered from the Insert page.
Users can manually change this from the bottom of that page to add or remove
blank rows.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ForeignKeyMaxLimit">
<code class="sig-name descname">$cfg['ForeignKeyMaxLimit']</code><a class="headerlink" href="#cfg_ForeignKeyMaxLimit" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>100</p>
</dd>
</dl>
<p>If there are fewer items than this in the set of foreign keys, then a
drop-down box of foreign keys is presented, in the style described by
the <span class="target" id="index-148"></span><a class="reference internal" href="#cfg_ForeignKeyDropdownOrder"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['ForeignKeyDropdownOrder']</span></code></a> setting.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ForeignKeyDropdownOrder">
<code class="sig-name descname">$cfg['ForeignKeyDropdownOrder']</code><a class="headerlink" href="#cfg_ForeignKeyDropdownOrder" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(‘content-id’, ‘id-content’)</p>
</dd>
</dl>
<p>For the foreign key drop-down fields, there are several methods of
display, offering both the key and value data. The contents of the
array should be one or both of the following strings: <code class="docutils literal notranslate"><span class="pre">content-id</span></code>,
<code class="docutils literal notranslate"><span class="pre">id-content</span></code>.</p>
</dd></dl>

</div>
<div class="section" id="export-and-import-settings">
<h2>Export and import settings<a class="headerlink" href="#export-and-import-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_ZipDump">
<code class="sig-name descname">$cfg['ZipDump']</code><a class="headerlink" href="#cfg_ZipDump" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_GZipDump">
<code class="sig-name descname">$cfg['GZipDump']</code><a class="headerlink" href="#cfg_GZipDump" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_BZipDump">
<code class="sig-name descname">$cfg['BZipDump']</code><a class="headerlink" href="#cfg_BZipDump" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to allow the use of zip/GZip/BZip2 compression when
creating a dump file</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_CompressOnFly">
<code class="sig-name descname">$cfg['CompressOnFly']</code><a class="headerlink" href="#cfg_CompressOnFly" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether to allow on the fly compression for GZip/BZip2
compressed exports. This doesn’t affect smaller dumps and allows users
to create larger dumps that won’t otherwise fit in memory due to php
memory limit. Produced files contain more GZip/BZip2 headers, but all
normal programs handle this correctly.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export">
<code class="sig-name descname">$cfg['Export']</code><a class="headerlink" href="#cfg_Export" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(…)</p>
</dd>
</dl>
<p>In this array are defined default parameters for export, names of
items are similar to texts seen on export page, so you can easily
identify what they mean.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export_format">
<code class="sig-name descname">$cfg['Export']['format']</code><a class="headerlink" href="#cfg_Export_format" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'sql'</span></code></p>
</dd>
</dl>
<p>Default export format.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export_method">
<code class="sig-name descname">$cfg['Export']['method']</code><a class="headerlink" href="#cfg_Export_method" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'quick'</span></code></p>
</dd>
</dl>
<p>Defines how the export form is displayed when it loads. Valid values
are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">quick</span></code> to display the minimum number of options to configure</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">custom</span></code> to display every available option to configure</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">custom-no-form</span></code> same as <code class="docutils literal notranslate"><span class="pre">custom</span></code> but does not display the option
of using quick export</p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export_charset">
<code class="sig-name descname">$cfg['Export']['charset']</code><a class="headerlink" href="#cfg_Export_charset" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Defines charset for generated export. By default no charset conversion is
done assuming UTF-8.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export_file_template_table">
<code class="sig-name descname">$cfg['Export']['file_template_table']</code><a class="headerlink" href="#cfg_Export_file_template_table" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'&#64;TABLE&#64;'</span></code></p>
</dd>
</dl>
<p>Default filename template for table exports.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faq6-27"><span class="std std-ref">6.27 What format strings can I use?</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export_file_template_database">
<code class="sig-name descname">$cfg['Export']['file_template_database']</code><a class="headerlink" href="#cfg_Export_file_template_database" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'&#64;DATABASE&#64;'</span></code></p>
</dd>
</dl>
<p>Default filename template for database exports.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faq6-27"><span class="std std-ref">6.27 What format strings can I use?</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export_file_template_server">
<code class="sig-name descname">$cfg['Export']['file_template_server']</code><a class="headerlink" href="#cfg_Export_file_template_server" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'&#64;SERVER&#64;'</span></code></p>
</dd>
</dl>
<p>Default filename template for server exports.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faq6-27"><span class="std std-ref">6.27 What format strings can I use?</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Export_remove_definer_from_definitions">
<code class="sig-name descname">$cfg['Export']['remove_definer_from_definitions']</code><a class="headerlink" href="#cfg_Export_remove_definer_from_definitions" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Remove DEFINER clause from the event, view and routine definitions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 5.2.0.</span></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_Import">
<code class="sig-name descname">$cfg['Import']</code><a class="headerlink" href="#cfg_Import" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(…)</p>
</dd>
</dl>
<p>In this array are defined default parameters for import, names of
items are similar to texts seen on import page, so you can easily
identify what they mean.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Import_charset">
<code class="sig-name descname">$cfg['Import']['charset']</code><a class="headerlink" href="#cfg_Import_charset" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Defines charset for import. By default no charset conversion is done
assuming UTF-8.</p>
</dd></dl>

</div>
<div class="section" id="tabs-display-settings">
<h2>Tabs display settings<a class="headerlink" href="#tabs-display-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_TabsMode">
<code class="sig-name descname">$cfg['TabsMode']</code><a class="headerlink" href="#cfg_TabsMode" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'both'</span></code></p>
</dd>
</dl>
<p>Defines whether the menu tabs contain <code class="docutils literal notranslate"><span class="pre">'icons'</span></code>, <code class="docutils literal notranslate"><span class="pre">'text'</span></code> or <code class="docutils literal notranslate"><span class="pre">'both'</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_PropertiesNumColumns">
<code class="sig-name descname">$cfg['PropertiesNumColumns']</code><a class="headerlink" href="#cfg_PropertiesNumColumns" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>1</p>
</dd>
</dl>
<p>How many columns will be utilized to display the tables on the database
property view? When setting this to a value larger than 1, the type of the
database will be omitted for more display space.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTabServer">
<code class="sig-name descname">$cfg['DefaultTabServer']</code><a class="headerlink" href="#cfg_DefaultTabServer" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'welcome'</span></code></p>
</dd>
</dl>
<p>Defines the tab displayed by default on server view. The possible values
are the localized equivalent of:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">welcome</span></code> (recommended for multi-user setups)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">databases</span></code>,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">status</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">variables</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">privileges</span></code></p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTabDatabase">
<code class="sig-name descname">$cfg['DefaultTabDatabase']</code><a class="headerlink" href="#cfg_DefaultTabDatabase" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'structure'</span></code></p>
</dd>
</dl>
<p>Defines the tab displayed by default on database view. The possible values
are the localized equivalent of:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">structure</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sql</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">search</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">operations</span></code></p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTabTable">
<code class="sig-name descname">$cfg['DefaultTabTable']</code><a class="headerlink" href="#cfg_DefaultTabTable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'browse'</span></code></p>
</dd>
</dl>
<p>Defines the tab displayed by default on table view. The possible values
are the localized equivalent of:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">structure</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">sql</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">search</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">insert</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">browse</span></code></p></li>
</ul>
</dd></dl>

</div>
<div class="section" id="pdf-options">
<h2>PDF Options<a class="headerlink" href="#pdf-options" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_PDFPageSizes">
<code class="sig-name descname">$cfg['PDFPageSizes']</code><a class="headerlink" href="#cfg_PDFPageSizes" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">array('A3',</span> <span class="pre">'A4',</span> <span class="pre">'A5',</span> <span class="pre">'letter',</span> <span class="pre">'legal')</span></code></p>
</dd>
</dl>
<p>Array of possible paper sizes for creating PDF pages.</p>
<p>You should never need to change this.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_PDFDefaultPageSize">
<code class="sig-name descname">$cfg['PDFDefaultPageSize']</code><a class="headerlink" href="#cfg_PDFDefaultPageSize" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'A4'</span></code></p>
</dd>
</dl>
<p>Default page size to use when creating PDF pages. Valid values are any
listed in <span class="target" id="index-149"></span><a class="reference internal" href="#cfg_PDFPageSizes"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['PDFPageSizes']</span></code></a>.</p>
</dd></dl>

</div>
<div class="section" id="languages">
<h2>Languages<a class="headerlink" href="#languages" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_DefaultLang">
<code class="sig-name descname">$cfg['DefaultLang']</code><a class="headerlink" href="#cfg_DefaultLang" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'en'</span></code></p>
</dd>
</dl>
<p>Defines the default language to use, if not browser-defined or user-
defined. The corresponding language file needs to be in
locale/<em>code</em>/LC_MESSAGES/phpmyadmin.mo.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultConnectionCollation">
<code class="sig-name descname">$cfg['DefaultConnectionCollation']</code><a class="headerlink" href="#cfg_DefaultConnectionCollation" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'utf8mb4_general_ci'</span></code></p>
</dd>
</dl>
<p>Defines the default connection collation to use, if not user-defined.
See the <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/charset-charsets.html">MySQL documentation for charsets</a>
for list of possible values.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Lang">
<code class="sig-name descname">$cfg['Lang']</code><a class="headerlink" href="#cfg_Lang" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>not set</p>
</dd>
</dl>
<p>Force language to use. The corresponding language file needs to be in
locale/<em>code</em>/LC_MESSAGES/phpmyadmin.mo.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_FilterLanguages">
<code class="sig-name descname">$cfg['FilterLanguages']</code><a class="headerlink" href="#cfg_FilterLanguages" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Limit list of available languages to those matching the given regular
expression. For example if you want only Czech and English, you should
set filter to <code class="docutils literal notranslate"><span class="pre">'^(cs|en)'</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_RecodingEngine">
<code class="sig-name descname">$cfg['RecodingEngine']</code><a class="headerlink" href="#cfg_RecodingEngine" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'auto'</span></code></p>
</dd>
</dl>
<p>You can select here which functions will be used for character set
conversion. Possible values are:</p>
<ul class="simple">
<li><p>auto - automatically use available one (first is tested iconv, then
recode)</p></li>
<li><p>iconv - use iconv or libiconv functions</p></li>
<li><p>recode - use recode_string function</p></li>
<li><p>mb - use <a class="reference internal" href="glossary.html#term-mbstring"><span class="xref std std-term">mbstring</span></a> extension</p></li>
<li><p>none - disable encoding conversion</p></li>
</ul>
<p>Enabled charset conversion activates a pull-down menu in the Export
and Import pages, to choose the character set when exporting a file.
The default value in this menu comes from
<span class="target" id="index-150"></span><a class="reference internal" href="#cfg_Export_charset"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Export']['charset']</span></code></a> and <span class="target" id="index-151"></span><a class="reference internal" href="#cfg_Import_charset"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Import']['charset']</span></code></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_IconvExtraParams">
<code class="sig-name descname">$cfg['IconvExtraParams']</code><a class="headerlink" href="#cfg_IconvExtraParams" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'//TRANSLIT'</span></code></p>
</dd>
</dl>
<p>Specify some parameters for iconv used in charset conversion. See
<a class="reference external" href="https://www.gnu.org/savannah-checkouts/gnu/libiconv/documentation/libiconv-1.15/iconv_open.3.html">iconv documentation</a> for details. By default
<code class="docutils literal notranslate"><span class="pre">//TRANSLIT</span></code> is used, so that invalid characters will be
transliterated.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_AvailableCharsets">
<code class="sig-name descname">$cfg['AvailableCharsets']</code><a class="headerlink" href="#cfg_AvailableCharsets" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(…)</p>
</dd>
</dl>
<p>Available character sets for MySQL conversion. You can add your own
(any of supported by recode/iconv) or remove these which you don’t
use. Character sets will be shown in same order as here listed, so if
you frequently use some of these move them to the top.</p>
</dd></dl>

</div>
<div class="section" id="web-server-settings">
<h2>Web server settings<a class="headerlink" href="#web-server-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_OBGzip">
<code class="sig-name descname">$cfg['OBGzip']</code><a class="headerlink" href="#cfg_OBGzip" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string/boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'auto'</span></code></p>
</dd>
</dl>
<p>Defines whether to use GZip output buffering for increased speed in
<a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> transfers. Set to
true/false for enabling/disabling. When set to ‘auto’ (string),
phpMyAdmin tries to enable output buffering and will automatically
disable it if your browser has some problems with buffering. IE6 with
a certain patch is known to cause data corruption when having enabled
buffering.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_TrustedProxies">
<code class="sig-name descname">$cfg['TrustedProxies']</code><a class="headerlink" href="#cfg_TrustedProxies" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array()</p>
</dd>
</dl>
<p>Lists proxies and HTTP headers which are trusted for
<span class="target" id="index-152"></span><a class="reference internal" href="#cfg_Servers_AllowDeny_order"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['AllowDeny']['order']</span></code></a>. This list is by
default empty, you need to fill in some trusted proxy servers if you
want to use rules for IP addresses behind proxy.</p>
<p>The following example specifies that phpMyAdmin should trust a
HTTP_X_FORWARDED_FOR (<code class="docutils literal notranslate"><span class="pre">X-Forwarded-For</span></code>) header coming from the proxy
*******:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;TrustedProxies&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;*******&#39;</span> <span class="o">=&gt;</span> <span class="s1">&#39;HTTP_X_FORWARDED_FOR&#39;</span><span class="p">];</span>
</pre></div>
</div>
<p>The <span class="target" id="index-153"></span><a class="reference internal" href="#cfg_Servers_AllowDeny_rules"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['AllowDeny']['rules']</span></code></a> directive uses the
client’s IP address as usual.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_GD2Available">
<code class="sig-name descname">$cfg['GD2Available']</code><a class="headerlink" href="#cfg_GD2Available" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'auto'</span></code></p>
</dd>
</dl>
<p>Specifies whether GD &gt;= 2 is available. If yes it can be used for MIME
transformations. Possible values are:</p>
<ul class="simple">
<li><p>auto - automatically detect</p></li>
<li><p>yes - GD 2 functions can be used</p></li>
<li><p>no - GD 2 function cannot be used</p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_CheckConfigurationPermissions">
<code class="sig-name descname">$cfg['CheckConfigurationPermissions']</code><a class="headerlink" href="#cfg_CheckConfigurationPermissions" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>We normally check the permissions on the configuration file to ensure
it’s not world writable. However, phpMyAdmin could be installed on a
NTFS filesystem mounted on a non-Windows server, in which case the
permissions seems wrong but in fact cannot be detected. In this case a
sysadmin would set this parameter to <code class="docutils literal notranslate"><span class="pre">false</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_LinkLengthLimit">
<code class="sig-name descname">$cfg['LinkLengthLimit']</code><a class="headerlink" href="#cfg_LinkLengthLimit" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>1000</p>
</dd>
</dl>
<p>Limit for length of <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> in links.  When length would be above this
limit, it is replaced by form with button. This is required as some web
servers (<a class="reference internal" href="glossary.html#term-IIS"><span class="xref std std-term">IIS</span></a>) have problems with long <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> .</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_CSPAllow">
<code class="sig-name descname">$cfg['CSPAllow']</code><a class="headerlink" href="#cfg_CSPAllow" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Additional string to include in allowed script and image sources in Content
Security Policy header.</p>
<p>This can be useful when you want to include some external JavaScript files
in <code class="file docutils literal notranslate"><span class="pre">config.footer.inc.php</span></code> or <code class="file docutils literal notranslate"><span class="pre">config.header.inc.php</span></code>, which
would be normally not allowed by <a class="reference internal" href="glossary.html#term-Content-Security-Policy"><span class="xref std std-term">Content Security Policy</span></a>.</p>
<p>To allow some sites, just list them within the string:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CSPAllow&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;example.com example.net&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 4.0.4.</span></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_DisableMultiTableMaintenance">
<code class="sig-name descname">$cfg['DisableMultiTableMaintenance']</code><a class="headerlink" href="#cfg_DisableMultiTableMaintenance" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>In the database Structure page, it’s possible to mark some tables then
choose an operation like optimizing for many tables. This can slow
down a server; therefore, setting this to <code class="docutils literal notranslate"><span class="pre">true</span></code> prevents this kind
of multiple maintenance operation.</p>
</dd></dl>

</div>
<div class="section" id="theme-settings">
<h2>Theme settings<a class="headerlink" href="#theme-settings" title="Permalink to this headline">¶</a></h2>
<blockquote>
<div><p>Please directly modify <code class="file docutils literal notranslate"><span class="pre">themes/themename/scss/_variables.scss</span></code>, although
your changes will be overwritten with the next update.</p>
</div></blockquote>
</div>
<div class="section" id="design-customization">
<h2>Design customization<a class="headerlink" href="#design-customization" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_NavigationTreePointerEnable">
<code class="sig-name descname">$cfg['NavigationTreePointerEnable']</code><a class="headerlink" href="#cfg_NavigationTreePointerEnable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>When set to true, hovering over an item in the navigation panel causes that item to be marked
(the background is highlighted).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_BrowsePointerEnable">
<code class="sig-name descname">$cfg['BrowsePointerEnable']</code><a class="headerlink" href="#cfg_BrowsePointerEnable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>When set to true, hovering over a row in the Browse page causes that row to be marked (the background
is highlighted).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_BrowseMarkerEnable">
<code class="sig-name descname">$cfg['BrowseMarkerEnable']</code><a class="headerlink" href="#cfg_BrowseMarkerEnable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>When set to true, a data row is marked (the background is highlighted) when the row is selected
with the checkbox.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_LimitChars">
<code class="sig-name descname">$cfg['LimitChars']</code><a class="headerlink" href="#cfg_LimitChars" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>50</p>
</dd>
</dl>
<p>Maximum number of characters shown in any non-numeric field on browse
view. Can be turned off by a toggle button on the browse page.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_RowActionLinks">
<code class="sig-name descname">$cfg['RowActionLinks']</code><a class="headerlink" href="#cfg_RowActionLinks" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'left'</span></code></p>
</dd>
</dl>
<p>Defines the place where table row links (Edit, Copy, Delete) would be
put when tables contents are displayed (you may have them displayed at
the left side, right side, both sides or nowhere).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_RowActionLinksWithoutUnique">
<code class="sig-name descname">$cfg['RowActionLinksWithoutUnique']</code><a class="headerlink" href="#cfg_RowActionLinksWithoutUnique" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines whether to show row links (Edit, Copy, Delete) and checkboxes
for multiple row operations even when the selection does not have a <a class="reference internal" href="glossary.html#term-unique-key"><span class="xref std std-term">unique key</span></a>.
Using row actions in the absence of a unique key may result in different/more
rows being affected since there is no guaranteed way to select the exact row(s).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_RememberSorting">
<code class="sig-name descname">$cfg['RememberSorting']</code><a class="headerlink" href="#cfg_RememberSorting" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>If enabled, remember the sorting of each table when browsing them.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_TablePrimaryKeyOrder">
<code class="sig-name descname">$cfg['TablePrimaryKeyOrder']</code><a class="headerlink" href="#cfg_TablePrimaryKeyOrder" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'NONE'</span></code></p>
</dd>
</dl>
<p>This defines the default sort order for the tables, having a <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a>,
when there is no sort order defines externally.
Acceptable values : [‘NONE’, ‘ASC’, ‘DESC’]</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowBrowseComments">
<code class="sig-name descname">$cfg['ShowBrowseComments']</code><a class="headerlink" href="#cfg_ShowBrowseComments" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_ShowPropertyComments">
<code class="sig-name descname">$cfg['ShowPropertyComments']</code><a class="headerlink" href="#cfg_ShowPropertyComments" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>By setting the corresponding variable to <code class="docutils literal notranslate"><span class="pre">true</span></code> you can enable the
display of column comments in Browse or Property display. In browse
mode, the comments are shown inside the header. In property mode,
comments are displayed using a CSS-formatted dashed-line below the
name of the column. The comment is shown as a tool-tip for that
column.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_FirstDayOfCalendar">
<code class="sig-name descname">$cfg['FirstDayOfCalendar']</code><a class="headerlink" href="#cfg_FirstDayOfCalendar" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>0</p>
</dd>
</dl>
<p>This will define the first day of week in the calendar. The number
can be set from 0 to 6, which represents the seven days of the week,
Sunday to Saturday respectively. This value can also be configured by the user
in <span class="guilabel">Settings</span> -&gt; <span class="guilabel">Features</span> -&gt; <span class="guilabel">General</span> -&gt; <span class="guilabel">First day of calendar</span> field.</p>
</dd></dl>

</div>
<div class="section" id="text-fields">
<h2>Text fields<a class="headerlink" href="#text-fields" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_CharEditing">
<code class="sig-name descname">$cfg['CharEditing']</code><a class="headerlink" href="#cfg_CharEditing" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'input'</span></code></p>
</dd>
</dl>
<p>Defines which type of editing controls should be used for CHAR and
VARCHAR columns. Applies to data editing and also to the default values
in structure editing. Possible values are:</p>
<ul class="simple">
<li><p>input - this allows to limit size of text to size of columns in MySQL,
but has problems with newlines in columns</p></li>
<li><p>textarea - no problems with newlines in columns, but also no length
limitations</p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_MinSizeForInputField">
<code class="sig-name descname">$cfg['MinSizeForInputField']</code><a class="headerlink" href="#cfg_MinSizeForInputField" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>4</p>
</dd>
</dl>
<p>Defines the minimum size for input fields generated for CHAR and
VARCHAR columns.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxSizeForInputField">
<code class="sig-name descname">$cfg['MaxSizeForInputField']</code><a class="headerlink" href="#cfg_MaxSizeForInputField" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>60</p>
</dd>
</dl>
<p>Defines the maximum size for input fields generated for CHAR and
VARCHAR columns.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_TextareaCols">
<code class="sig-name descname">$cfg['TextareaCols']</code><a class="headerlink" href="#cfg_TextareaCols" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>40</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_TextareaRows">
<code class="sig-name descname">$cfg['TextareaRows']</code><a class="headerlink" href="#cfg_TextareaRows" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>15</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_CharTextareaCols">
<code class="sig-name descname">$cfg['CharTextareaCols']</code><a class="headerlink" href="#cfg_CharTextareaCols" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>40</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_CharTextareaRows">
<code class="sig-name descname">$cfg['CharTextareaRows']</code><a class="headerlink" href="#cfg_CharTextareaRows" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>7</p>
</dd>
</dl>
<p>Number of columns and rows for the textareas. This value will be
emphasized (*2) for <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> query
textareas and (*1.25) for <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a>
textareas inside the query window.</p>
<p>The Char* values are used for CHAR
and VARCHAR editing (if configured via <span class="target" id="index-154"></span><a class="reference internal" href="#cfg_CharEditing"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['CharEditing']</span></code></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 5.0.0: </span>The default value was changed from 2 to 7.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_LongtextDoubleTextarea">
<code class="sig-name descname">$cfg['LongtextDoubleTextarea']</code><a class="headerlink" href="#cfg_LongtextDoubleTextarea" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Defines whether textarea for LONGTEXT columns should have double size.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_TextareaAutoSelect">
<code class="sig-name descname">$cfg['TextareaAutoSelect']</code><a class="headerlink" href="#cfg_TextareaAutoSelect" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Defines if the whole textarea of the query box will be selected on
click.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_EnableAutocompleteForTablesAndColumns">
<code class="sig-name descname">$cfg['EnableAutocompleteForTablesAndColumns']</code><a class="headerlink" href="#cfg_EnableAutocompleteForTablesAndColumns" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to enable autocomplete for table and column names in any
SQL query box.</p>
</dd></dl>

</div>
<div class="section" id="sql-query-box-settings">
<h2>SQL query box settings<a class="headerlink" href="#sql-query-box-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_SQLQuery_Edit">
<code class="sig-name descname">$cfg['SQLQuery']['Edit']</code><a class="headerlink" href="#cfg_SQLQuery_Edit" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to display an edit link to change a query in any SQL Query
box.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SQLQuery_Explain">
<code class="sig-name descname">$cfg['SQLQuery']['Explain']</code><a class="headerlink" href="#cfg_SQLQuery_Explain" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to display a link to explain a SELECT query in any SQL Query
box.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SQLQuery_ShowAsPHP">
<code class="sig-name descname">$cfg['SQLQuery']['ShowAsPHP']</code><a class="headerlink" href="#cfg_SQLQuery_ShowAsPHP" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to display a link to wrap a query in PHP code in any SQL Query
box.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_SQLQuery_Refresh">
<code class="sig-name descname">$cfg['SQLQuery']['Refresh']</code><a class="headerlink" href="#cfg_SQLQuery_Refresh" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Whether to display a link to refresh a query in any SQL Query box.</p>
</dd></dl>

</div>
<div class="section" id="web-server-upload-save-import-directories">
<span id="web-dirs"></span><h2>Web server upload/save/import directories<a class="headerlink" href="#web-server-upload-save-import-directories" title="Permalink to this headline">¶</a></h2>
<p>If PHP is running in safe mode, all directories must be owned by the same user
as the owner of the phpMyAdmin scripts.</p>
<p>If the directory where phpMyAdmin is installed is subject to an
<code class="docutils literal notranslate"><span class="pre">open_basedir</span></code> restriction, you need to create a temporary directory in some
directory accessible by the PHP interpreter.</p>
<p>For security reasons, all directories should be outside the tree published by
webserver. If you cannot avoid having this directory published by webserver,
limit access to it either by web server configuration (for example using
.htaccess or web.config files) or place at least an empty <code class="file docutils literal notranslate"><span class="pre">index.html</span></code>
file there, so that directory listing is not possible. However as long as the
directory is accessible by web server, an attacker can guess filenames to download
the files.</p>
<dl class="config option">
<dt id="cfg_UploadDir">
<code class="sig-name descname">$cfg['UploadDir']</code><a class="headerlink" href="#cfg_UploadDir" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The name of the directory where <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> files have been uploaded by
other means than phpMyAdmin (for example, FTP). Those files are available
under a drop-down box when you click the database or table name, then the
Import tab.</p>
<p>If
you want different directory for each user, %u will be replaced with
username.</p>
<p>Please note that the file names must have the suffix “.sql”
(or “.sql.bz2” or “.sql.gz” if support for compressed formats is
enabled).</p>
<p>This feature is useful when your file is too big to be
uploaded via <a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a>, or when file
uploads are disabled in PHP.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Please see top of this chapter (<a class="reference internal" href="#web-dirs"><span class="std std-ref">Web server upload/save/import directories</span></a>) for instructions how
to setup this directory and how to make its usage secure.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>See <a class="reference internal" href="faq.html#faq1-16"><span class="std std-ref">1.16 I cannot upload big dump files (memory, HTTP or timeout problems).</span></a> for alternatives.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_SaveDir">
<code class="sig-name descname">$cfg['SaveDir']</code><a class="headerlink" href="#cfg_SaveDir" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>The name of the webserver directory where exported files can be saved.</p>
<p>If you want a different directory for each user, %u will be replaced with the
username.</p>
<p>Please note that the directory must exist and has to be writable for
the user running webserver.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Please see top of this chapter (<a class="reference internal" href="#web-dirs"><span class="std std-ref">Web server upload/save/import directories</span></a>) for instructions how
to setup this directory and how to make its usage secure.</p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_TempDir">
<code class="sig-name descname">$cfg['TempDir']</code><a class="headerlink" href="#cfg_TempDir" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'./tmp/'</span></code></p>
</dd>
</dl>
<p>The name of the directory where temporary files can be stored. It is used
for several purposes, currently:</p>
<ul class="simple">
<li><p>The templates cache which speeds up page loading.</p></li>
<li><p>ESRI Shapefiles import, see <a class="reference internal" href="faq.html#faq6-30"><span class="std std-ref">6.30 Import: How can I import ESRI Shapefiles?</span></a>.</p></li>
<li><p>To work around limitations of <code class="docutils literal notranslate"><span class="pre">open_basedir</span></code> for uploaded files, see
<a class="reference internal" href="faq.html#faq1-11"><span class="std std-ref">1.11 I get an ‘open_basedir restriction’ while uploading a file from the import tab.</span></a>.</p></li>
</ul>
<p>This directory should have as strict permissions as possible as the only
user required to access this directory is the one who runs the webserver.
If you have root privileges, simply make this user owner of this directory
and make it accessible only by it:</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span>chown www-data:www-data tmp
chmod <span class="m">700</span> tmp
</pre></div>
</div>
<p>If you cannot change owner of the directory, you can achieve a similar
setup using <a class="reference internal" href="glossary.html#term-ACL"><span class="xref std std-term">ACL</span></a>:</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span>chmod <span class="m">700</span> tmp
setfacl -m <span class="s2">&quot;g:www-data:rwx&quot;</span> tmp
setfacl -d -m <span class="s2">&quot;g:www-data:rwx&quot;</span> tmp
</pre></div>
</div>
<p>If neither of above works for you, you can still make the directory
<strong class="command">chmod 777</strong>, but it might impose risk of other users on system
reading and writing data in this directory.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Please see top of this chapter (<a class="reference internal" href="#web-dirs"><span class="std std-ref">Web server upload/save/import directories</span></a>) for instructions how
to setup this directory and how to make its usage secure.</p>
</div>
</dd></dl>

</div>
<div class="section" id="various-display-setting">
<h2>Various display setting<a class="headerlink" href="#various-display-setting" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_RepeatCells">
<code class="sig-name descname">$cfg['RepeatCells']</code><a class="headerlink" href="#cfg_RepeatCells" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>100</p>
</dd>
</dl>
<p>Repeat the headers every X cells, or 0 to deactivate.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_QueryHistoryDB">
<code class="sig-name descname">$cfg['QueryHistoryDB']</code><a class="headerlink" href="#cfg_QueryHistoryDB" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_QueryHistoryMax">
<code class="sig-name descname">$cfg['QueryHistoryMax']</code><a class="headerlink" href="#cfg_QueryHistoryMax" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>25</p>
</dd>
</dl>
<p>If <span class="target" id="index-155"></span><a class="reference internal" href="#cfg_QueryHistoryDB"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['QueryHistoryDB']</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code>, all your
Queries are logged to a table, which has to be created by you (see
<span class="target" id="index-156"></span><a class="reference internal" href="#cfg_Servers_history"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['history']</span></code></a>). If set to false, all your
queries will be appended to the form, but only as long as your window is
opened they remain saved.</p>
<p>When using the JavaScript based query window, it will always get updated
when you click on a new table/db to browse and will focus if you click on
<span class="guilabel">Edit SQL</span> after using a query. You can suppress updating the
query window by checking the box <span class="guilabel">Do not overwrite this query
from outside the window</span> below the query textarea. Then you can browse
tables/databases in the background without losing the contents of the
textarea, so this is especially useful when composing a query with tables
you first have to look in. The checkbox will get automatically checked
whenever you change the contents of the textarea. Please uncheck the button
whenever you definitely want the query window to get updated even though
you have made alterations.</p>
<p>If <span class="target" id="index-157"></span><a class="reference internal" href="#cfg_QueryHistoryDB"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['QueryHistoryDB']</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">true</span></code> you can
specify the amount of saved history items using
<span class="target" id="index-158"></span><a class="reference internal" href="#cfg_QueryHistoryMax"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['QueryHistoryMax']</span></code></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_BrowseMIME">
<code class="sig-name descname">$cfg['BrowseMIME']</code><a class="headerlink" href="#cfg_BrowseMIME" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Enable <a class="reference internal" href="transformations.html#transformations"><span class="std std-ref">Transformations</span></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxExactCount">
<code class="sig-name descname">$cfg['MaxExactCount']</code><a class="headerlink" href="#cfg_MaxExactCount" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>50000</p>
</dd>
</dl>
<p>For InnoDB tables, determines for how large tables phpMyAdmin should
get the exact row count using <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">COUNT</span></code>. If the approximate row
count as returned by <code class="docutils literal notranslate"><span class="pre">SHOW</span> <span class="pre">TABLE</span> <span class="pre">STATUS</span></code> is smaller than this value,
<code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">COUNT</span></code> will be used, otherwise the approximate count will be
used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 4.8.0: </span>The default value was lowered to 50000 for performance reasons.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 4.2.6: </span>The default value was changed to 500000.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faq3-11"><span class="std std-ref">3.11 The number of rows for InnoDB tables is not correct.</span></a></p>
</div>
</dd></dl>

<dl class="config option">
<dt id="cfg_MaxExactCountViews">
<code class="sig-name descname">$cfg['MaxExactCountViews']</code><a class="headerlink" href="#cfg_MaxExactCountViews" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>0</p>
</dd>
</dl>
<p>For VIEWs, since obtaining the exact count could have an impact on
performance, this value is the maximum to be displayed, using a
<code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">COUNT</span> <span class="pre">...</span> <span class="pre">LIMIT</span></code>. Setting this to 0 bypasses any row
counting.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_NaturalOrder">
<code class="sig-name descname">$cfg['NaturalOrder']</code><a class="headerlink" href="#cfg_NaturalOrder" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Sorts database and table names according to natural order (for
example, t1, t2, t10). Currently implemented in the navigation panel
and in Database view, for the table list.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_InitialSlidersState">
<code class="sig-name descname">$cfg['InitialSlidersState']</code><a class="headerlink" href="#cfg_InitialSlidersState" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'closed'</span></code></p>
</dd>
</dl>
<p>If set to <code class="docutils literal notranslate"><span class="pre">'closed'</span></code>, the visual sliders are initially in a closed
state. A value of <code class="docutils literal notranslate"><span class="pre">'open'</span></code> does the reverse. To completely disable
all visual sliders, use <code class="docutils literal notranslate"><span class="pre">'disabled'</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_UserprefsDisallow">
<code class="sig-name descname">$cfg['UserprefsDisallow']</code><a class="headerlink" href="#cfg_UserprefsDisallow" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array()</p>
</dd>
</dl>
<p>Contains names of configuration options (keys in <code class="docutils literal notranslate"><span class="pre">$cfg</span></code> array) that
users can’t set through user preferences. For possible values, refer
to classes under <code class="file docutils literal notranslate"><span class="pre">libraries/classes/Config/Forms/User/</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_UserprefsDeveloperTab">
<code class="sig-name descname">$cfg['UserprefsDeveloperTab']</code><a class="headerlink" href="#cfg_UserprefsDeveloperTab" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.0.</span></p>
</div>
<p>Activates in the user preferences a tab containing options for
developers of phpMyAdmin.</p>
</dd></dl>

</div>
<div class="section" id="page-titles">
<h2>Page titles<a class="headerlink" href="#page-titles" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_TitleTable">
<code class="sig-name descname">$cfg['TitleTable']</code><a class="headerlink" href="#cfg_TitleTable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'&#64;HTTP_HOST&#64;</span> <span class="pre">/</span> <span class="pre">&#64;VSERVER&#64;</span> <span class="pre">/</span> <span class="pre">&#64;DATABASE&#64;</span> <span class="pre">/</span> <span class="pre">&#64;TABLE&#64;</span> <span class="pre">|</span> <span class="pre">&#64;PHPMYADMIN&#64;'</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_TitleDatabase">
<code class="sig-name descname">$cfg['TitleDatabase']</code><a class="headerlink" href="#cfg_TitleDatabase" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'&#64;HTTP_HOST&#64;</span> <span class="pre">/</span> <span class="pre">&#64;VSERVER&#64;</span> <span class="pre">/</span> <span class="pre">&#64;DATABASE&#64;</span> <span class="pre">|</span> <span class="pre">&#64;PHPMYADMIN&#64;'</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_TitleServer">
<code class="sig-name descname">$cfg['TitleServer']</code><a class="headerlink" href="#cfg_TitleServer" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'&#64;HTTP_HOST&#64;</span> <span class="pre">/</span> <span class="pre">&#64;VSERVER&#64;</span> <span class="pre">|</span> <span class="pre">&#64;PHPMYADMIN&#64;'</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_TitleDefault">
<code class="sig-name descname">$cfg['TitleDefault']</code><a class="headerlink" href="#cfg_TitleDefault" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'&#64;HTTP_HOST&#64;</span> <span class="pre">|</span> <span class="pre">&#64;PHPMYADMIN&#64;'</span></code></p>
</dd>
</dl>
<p>Allows you to specify window’s title bar. You can use <a class="reference internal" href="faq.html#faq6-27"><span class="std std-ref">6.27 What format strings can I use?</span></a>.</p>
</dd></dl>

</div>
<div class="section" id="theme-manager-settings">
<h2>Theme manager settings<a class="headerlink" href="#theme-manager-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_ThemeManager">
<code class="sig-name descname">$cfg['ThemeManager']</code><a class="headerlink" href="#cfg_ThemeManager" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Enables user-selectable themes. See <a class="reference internal" href="faq.html#faqthemes"><span class="std std-ref">2.7 Using and creating themes</span></a>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ThemeDefault">
<code class="sig-name descname">$cfg['ThemeDefault']</code><a class="headerlink" href="#cfg_ThemeDefault" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'pmahomme'</span></code></p>
</dd>
</dl>
<p>The default theme (a subdirectory under <code class="file docutils literal notranslate"><span class="pre">./themes/</span></code>).</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_ThemePerServer">
<code class="sig-name descname">$cfg['ThemePerServer']</code><a class="headerlink" href="#cfg_ThemePerServer" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Whether to allow different theme for each server.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_FontSize">
<code class="sig-name descname">$cfg['FontSize']</code><a class="headerlink" href="#cfg_FontSize" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>‘82%’</p>
</dd>
</dl>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 5.0.0: </span>This setting was removed as the browser is more efficient,
thus no need of this option.</p>
</div>
<p>Font size to use, is applied in CSS.</p>
</dd></dl>

</div>
<div class="section" id="default-queries">
<h2>Default queries<a class="headerlink" href="#default-queries" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_DefaultQueryTable">
<code class="sig-name descname">$cfg['DefaultQueryTable']</code><a class="headerlink" href="#cfg_DefaultQueryTable" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'SELECT</span> <span class="pre">*</span> <span class="pre">FROM</span> <span class="pre">&#64;TABLE&#64;</span> <span class="pre">WHERE</span> <span class="pre">1'</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultQueryDatabase">
<code class="sig-name descname">$cfg['DefaultQueryDatabase']</code><a class="headerlink" href="#cfg_DefaultQueryDatabase" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">''</span></code></p>
</dd>
</dl>
<p>Default queries that will be displayed in query boxes when user didn’t
specify any. You can use standard <a class="reference internal" href="faq.html#faq6-27"><span class="std std-ref">6.27 What format strings can I use?</span></a>.</p>
</dd></dl>

</div>
<div class="section" id="mysql-settings">
<h2>MySQL settings<a class="headerlink" href="#mysql-settings" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_DefaultFunctions">
<code class="sig-name descname">$cfg['DefaultFunctions']</code><a class="headerlink" href="#cfg_DefaultFunctions" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">array('FUNC_CHAR'</span> <span class="pre">=&gt;</span> <span class="pre">'',</span> <span class="pre">'FUNC_DATE'</span> <span class="pre">=&gt;</span> <span class="pre">'',</span> <span class="pre">'FUNC_NUMBER'</span> <span class="pre">=&gt;</span> <span class="pre">'',</span> <span class="pre">'FUNC_SPATIAL'</span> <span class="pre">=&gt;</span> <span class="pre">'GeomFromText',</span> <span class="pre">'FUNC_UUID'</span> <span class="pre">=&gt;</span> <span class="pre">'UUID',</span> <span class="pre">'first_timestamp'</span> <span class="pre">=&gt;</span> <span class="pre">'NOW')</span></code></p>
</dd>
</dl>
<p>Functions selected by default when inserting/changing row, Functions
are defined for meta types as (<code class="docutils literal notranslate"><span class="pre">FUNC_NUMBER</span></code>, <code class="docutils literal notranslate"><span class="pre">FUNC_DATE</span></code>, <code class="docutils literal notranslate"><span class="pre">FUNC_CHAR</span></code>,
<code class="docutils literal notranslate"><span class="pre">FUNC_SPATIAL</span></code>, <code class="docutils literal notranslate"><span class="pre">FUNC_UUID</span></code>) and for <code class="docutils literal notranslate"><span class="pre">first_timestamp</span></code>, which is used
for first timestamp column in table.</p>
<p>Example configuration</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;DefaultFunctions&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;FUNC_CHAR&#39;</span> <span class="o">=&gt;</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
    <span class="s1">&#39;FUNC_DATE&#39;</span> <span class="o">=&gt;</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
    <span class="s1">&#39;FUNC_NUMBER&#39;</span> <span class="o">=&gt;</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
    <span class="s1">&#39;FUNC_SPATIAL&#39;</span> <span class="o">=&gt;</span> <span class="s1">&#39;ST_GeomFromText&#39;</span><span class="p">,</span>
    <span class="s1">&#39;FUNC_UUID&#39;</span> <span class="o">=&gt;</span> <span class="s1">&#39;UUID&#39;</span><span class="p">,</span>
    <span class="s1">&#39;first_timestamp&#39;</span> <span class="o">=&gt;</span> <span class="s1">&#39;UTC_TIMESTAMP&#39;</span><span class="p">,</span>
<span class="p">];</span>
</pre></div>
</div>
</dd></dl>

</div>
<div class="section" id="default-options-for-transformations">
<h2>Default options for Transformations<a class="headerlink" href="#default-options-for-transformations" title="Permalink to this headline">¶</a></h2>
<dl class="config option">
<dt id="cfg_DefaultTransformations">
<code class="sig-name descname">$cfg['DefaultTransformations']</code><a class="headerlink" href="#cfg_DefaultTransformations" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>An array with below listed key-values</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_Substring">
<code class="sig-name descname">$cfg['DefaultTransformations']['Substring']</code><a class="headerlink" href="#cfg_DefaultTransformations_Substring" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(0, ‘all’, ‘…’)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_Bool2Text">
<code class="sig-name descname">$cfg['DefaultTransformations']['Bool2Text']</code><a class="headerlink" href="#cfg_DefaultTransformations_Bool2Text" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(‘T’, ‘F’)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_External">
<code class="sig-name descname">$cfg['DefaultTransformations']['External']</code><a class="headerlink" href="#cfg_DefaultTransformations_External" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(0, ‘-f /dev/null -i -wrap -q’, 1, 1)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_PreApPend">
<code class="sig-name descname">$cfg['DefaultTransformations']['PreApPend']</code><a class="headerlink" href="#cfg_DefaultTransformations_PreApPend" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(‘’, ‘’)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_Hex">
<code class="sig-name descname">$cfg['DefaultTransformations']['Hex']</code><a class="headerlink" href="#cfg_DefaultTransformations_Hex" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(‘2’)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_DateFormat">
<code class="sig-name descname">$cfg['DefaultTransformations']['DateFormat']</code><a class="headerlink" href="#cfg_DefaultTransformations_DateFormat" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(0, ‘’, ‘local’)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_Inline">
<code class="sig-name descname">$cfg['DefaultTransformations']['Inline']</code><a class="headerlink" href="#cfg_DefaultTransformations_Inline" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(‘100’, 100)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_TextImageLink">
<code class="sig-name descname">$cfg['DefaultTransformations']['TextImageLink']</code><a class="headerlink" href="#cfg_DefaultTransformations_TextImageLink" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(‘’, 100, 50)</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DefaultTransformations_TextLink">
<code class="sig-name descname">$cfg['DefaultTransformations']['TextLink']</code><a class="headerlink" href="#cfg_DefaultTransformations_TextLink" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>array(‘’, ‘’, ‘’)</p>
</dd>
</dl>
</dd></dl>

</div>
<div class="section" id="console-settings">
<h2>Console settings<a class="headerlink" href="#console-settings" title="Permalink to this headline">¶</a></h2>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These settings are mostly meant to be changed by user.</p>
</div>
<dl class="config option">
<dt id="cfg_Console_StartHistory">
<code class="sig-name descname">$cfg['Console']['StartHistory']</code><a class="headerlink" href="#cfg_Console_StartHistory" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Show query history at start</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Console_AlwaysExpand">
<code class="sig-name descname">$cfg['Console']['AlwaysExpand']</code><a class="headerlink" href="#cfg_Console_AlwaysExpand" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Always expand query messages</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Console_CurrentQuery">
<code class="sig-name descname">$cfg['Console']['CurrentQuery']</code><a class="headerlink" href="#cfg_Console_CurrentQuery" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>true</p>
</dd>
</dl>
<p>Show current browsing query</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Console_EnterExecutes">
<code class="sig-name descname">$cfg['Console']['EnterExecutes']</code><a class="headerlink" href="#cfg_Console_EnterExecutes" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Execute queries on Enter and insert new line with Shift+Enter</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Console_DarkTheme">
<code class="sig-name descname">$cfg['Console']['DarkTheme']</code><a class="headerlink" href="#cfg_Console_DarkTheme" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Switch to dark theme</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Console_Mode">
<code class="sig-name descname">$cfg['Console']['Mode']</code><a class="headerlink" href="#cfg_Console_Mode" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>‘info’</p>
</dd>
</dl>
<p>Console mode</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_Console_Height">
<code class="sig-name descname">$cfg['Console']['Height']</code><a class="headerlink" href="#cfg_Console_Height" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>integer</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>92</p>
</dd>
</dl>
<p>Console height</p>
</dd></dl>

</div>
<div class="section" id="developer">
<h2>Developer<a class="headerlink" href="#developer" title="Permalink to this headline">¶</a></h2>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>These settings might have huge effect on performance or security.</p>
</div>
<dl class="config option">
<dt id="cfg_environment">
<code class="sig-name descname">$cfg['environment']</code><a class="headerlink" href="#cfg_environment" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>string</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">'production'</span></code></p>
</dd>
</dl>
<p>Sets the working environment.</p>
<p>This only needs to be changed when you are developing phpMyAdmin itself.
The <code class="docutils literal notranslate"><span class="pre">development</span></code> mode may display debug information in some places.</p>
<p>Possible values are <code class="docutils literal notranslate"><span class="pre">'production'</span></code> or <code class="docutils literal notranslate"><span class="pre">'development'</span></code>.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_DBG">
<code class="sig-name descname">$cfg['DBG']</code><a class="headerlink" href="#cfg_DBG" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>array</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>[]</p>
</dd>
</dl>
</dd></dl>

<dl class="config option">
<dt id="cfg_DBG_sql">
<code class="sig-name descname">$cfg['DBG']['sql']</code><a class="headerlink" href="#cfg_DBG_sql" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Enable logging queries and execution times to be
displayed in the console’s Debug SQL tab.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_DBG_sqllog">
<code class="sig-name descname">$cfg['DBG']['sqllog']</code><a class="headerlink" href="#cfg_DBG_sqllog" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Enable logging of queries and execution times to the syslog.
Requires <span class="target" id="index-159"></span><a class="reference internal" href="#cfg_DBG_sql"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['DBG']['sql']</span></code></a> to be enabled.</p>
</dd></dl>

<dl class="config option">
<dt id="cfg_DBG_demo">
<code class="sig-name descname">$cfg['DBG']['demo']</code><a class="headerlink" href="#cfg_DBG_demo" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Enable to let server present itself as demo server.
This is used for <a class="reference external" href="https://www.phpmyadmin.net/try/">phpMyAdmin demo server</a>.</p>
<p>It currently changes following behavior:</p>
<ul class="simple">
<li><p>There is welcome message on the main page.</p></li>
<li><p>There is footer information about demo server and used Git revision.</p></li>
<li><p>The setup script is enabled even with existing configuration.</p></li>
<li><p>The setup does not try to connect to the MySQL server.</p></li>
</ul>
</dd></dl>

<dl class="config option">
<dt id="cfg_DBG_simple2fa">
<code class="sig-name descname">$cfg['DBG']['simple2fa']</code><a class="headerlink" href="#cfg_DBG_simple2fa" title="Permalink to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Type</dt>
<dd class="field-odd"><p>boolean</p>
</dd>
<dt class="field-even">Default value</dt>
<dd class="field-even"><p>false</p>
</dd>
</dl>
<p>Can be used for testing two-factor authentication using <a class="reference internal" href="two_factor.html#simple2fa"><span class="std std-ref">Simple two-factor authentication</span></a>.</p>
</dd></dl>

</div>
<div class="section" id="examples">
<span id="config-examples"></span><h2>Examples<a class="headerlink" href="#examples" title="Permalink to this headline">¶</a></h2>
<p>See following configuration snippets for typical setups of phpMyAdmin.</p>
<div class="section" id="basic-example">
<h3>Basic example<a class="headerlink" href="#basic-example" title="Permalink to this headline">¶</a></h3>
<p>Example configuration file, which can be copied to <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> to
get some core configuration layout; it is distributed with phpMyAdmin as
<code class="file docutils literal notranslate"><span class="pre">config.sample.inc.php</span></code>. Please note that it does not contain all
configuration options, only the most frequently used ones.</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;?</span><span class="nx">php</span>
<span class="sd">/**</span>
<span class="sd"> * phpMyAdmin sample configuration, you can use it as base for</span>
<span class="sd"> * manual configuration. For easier setup you can use setup/</span>
<span class="sd"> *</span>
<span class="sd"> * All directives are explained in documentation in the doc/ folder</span>
<span class="sd"> * or at &lt;https://docs.phpmyadmin.net/&gt;.</span>
<span class="sd"> */</span>

<span class="k">declare</span><span class="p">(</span><span class="nx">strict_types</span><span class="o">=</span><span class="mi">1</span><span class="p">);</span>

<span class="sd">/**</span>
<span class="sd"> * This is needed for cookie based authentication to encrypt the cookie.</span>
<span class="sd"> * Needs to be a 32-bytes long string of random bytes. See FAQ 2.10.</span>
<span class="sd"> */</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;blowfish_secret&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="p">;</span> <span class="cm">/* YOU MUST FILL IN THIS FOR COOKIE AUTH! */</span>

<span class="sd">/**</span>
<span class="sd"> * Servers configuration</span>
<span class="sd"> */</span>
<span class="nv">$i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

<span class="sd">/**</span>
<span class="sd"> * First server</span>
<span class="sd"> */</span>
<span class="nv">$i</span><span class="o">++</span><span class="p">;</span>
<span class="cm">/* Authentication type */</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;auth_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;cookie&#39;</span><span class="p">;</span>
<span class="cm">/* Server parameters */</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;host&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;localhost&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;compress&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">false</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;AllowNoPassword&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">false</span><span class="p">;</span>

<span class="sd">/**</span>
<span class="sd"> * phpMyAdmin configuration storage settings.</span>
<span class="sd"> */</span>

<span class="cm">/* User used to manipulate with storage */</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;controlhost&#39;] = &#39;&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;controlport&#39;] = &#39;&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;controluser&#39;] = &#39;pma&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;controlpass&#39;] = &#39;pmapass&#39;;</span>

<span class="cm">/* Storage database and tables */</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;pmadb&#39;] = &#39;phpmyadmin&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;bookmarktable&#39;] = &#39;pma__bookmark&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;relation&#39;] = &#39;pma__relation&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;table_info&#39;] = &#39;pma__table_info&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;table_coords&#39;] = &#39;pma__table_coords&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;pdf_pages&#39;] = &#39;pma__pdf_pages&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;column_info&#39;] = &#39;pma__column_info&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;history&#39;] = &#39;pma__history&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;table_uiprefs&#39;] = &#39;pma__table_uiprefs&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;tracking&#39;] = &#39;pma__tracking&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;userconfig&#39;] = &#39;pma__userconfig&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;recent&#39;] = &#39;pma__recent&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;favorite&#39;] = &#39;pma__favorite&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;users&#39;] = &#39;pma__users&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;usergroups&#39;] = &#39;pma__usergroups&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;navigationhiding&#39;] = &#39;pma__navigationhiding&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;savedsearches&#39;] = &#39;pma__savedsearches&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;central_columns&#39;] = &#39;pma__central_columns&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;designer_settings&#39;] = &#39;pma__designer_settings&#39;;</span>
<span class="c1">// $cfg[&#39;Servers&#39;][$i][&#39;export_templates&#39;] = &#39;pma__export_templates&#39;;</span>

<span class="sd">/**</span>
<span class="sd"> * End of servers configuration</span>
<span class="sd"> */</span>

<span class="sd">/**</span>
<span class="sd"> * Directories for saving/loading files from server</span>
<span class="sd"> */</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;UploadDir&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;SaveDir&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="p">;</span>

<span class="sd">/**</span>
<span class="sd"> * Whether to display icons or text or both icons and text in table row</span>
<span class="sd"> * action segment. Value can be either of &#39;icons&#39;, &#39;text&#39; or &#39;both&#39;.</span>
<span class="sd"> * default = &#39;both&#39;</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;RowActionType&#39;] = &#39;icons&#39;;</span>

<span class="sd">/**</span>
<span class="sd"> * Defines whether a user should be displayed a &quot;show all (records)&quot;</span>
<span class="sd"> * button in browse mode or not.</span>
<span class="sd"> * default = false</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;ShowAll&#39;] = true;</span>

<span class="sd">/**</span>
<span class="sd"> * Number of rows displayed when browsing a result set. If the result</span>
<span class="sd"> * set contains more rows, &quot;Previous&quot; and &quot;Next&quot;.</span>
<span class="sd"> * Possible values: 25, 50, 100, 250, 500</span>
<span class="sd"> * default = 25</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;MaxRows&#39;] = 50;</span>

<span class="sd">/**</span>
<span class="sd"> * Disallow editing of binary fields</span>
<span class="sd"> * valid values are:</span>
<span class="sd"> *   false    allow editing</span>
<span class="sd"> *   &#39;blob&#39;   allow editing except for BLOB fields</span>
<span class="sd"> *   &#39;noblob&#39; disallow editing except for BLOB fields</span>
<span class="sd"> *   &#39;all&#39;    disallow editing</span>
<span class="sd"> * default = &#39;blob&#39;</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;ProtectBinary&#39;] = false;</span>

<span class="sd">/**</span>
<span class="sd"> * Default language to use, if not browser-defined or user-defined</span>
<span class="sd"> * (you find all languages in the locale folder)</span>
<span class="sd"> * uncomment the desired line:</span>
<span class="sd"> * default = &#39;en&#39;</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;DefaultLang&#39;] = &#39;en&#39;;</span>
<span class="c1">//$cfg[&#39;DefaultLang&#39;] = &#39;de&#39;;</span>

<span class="sd">/**</span>
<span class="sd"> * How many columns should be used for table display of a database?</span>
<span class="sd"> * (a value larger than 1 results in some information being hidden)</span>
<span class="sd"> * default = 1</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;PropertiesNumColumns&#39;] = 2;</span>

<span class="sd">/**</span>
<span class="sd"> * Set to true if you want DB-based query history.If false, this utilizes</span>
<span class="sd"> * JS-routines to display query history (lost by window close)</span>
<span class="sd"> *</span>
<span class="sd"> * This requires configuration storage enabled, see above.</span>
<span class="sd"> * default = false</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;QueryHistoryDB&#39;] = true;</span>

<span class="sd">/**</span>
<span class="sd"> * When using DB-based query history, how many entries should be kept?</span>
<span class="sd"> * default = 25</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;QueryHistoryMax&#39;] = 100;</span>

<span class="sd">/**</span>
<span class="sd"> * Whether or not to query the user before sending the error report to</span>
<span class="sd"> * the phpMyAdmin team when a JavaScript error occurs</span>
<span class="sd"> *</span>
<span class="sd"> * Available options</span>
<span class="sd"> * (&#39;ask&#39; | &#39;always&#39; | &#39;never&#39;)</span>
<span class="sd"> * default = &#39;ask&#39;</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;SendErrorReports&#39;] = &#39;always&#39;;</span>

<span class="sd">/**</span>
<span class="sd"> * &#39;URLQueryEncryption&#39; defines whether phpMyAdmin will encrypt sensitive data from the URL query string.</span>
<span class="sd"> * &#39;URLQueryEncryptionSecretKey&#39; is a 32 bytes long secret key used to encrypt/decrypt the URL query string.</span>
<span class="sd"> */</span>
<span class="c1">//$cfg[&#39;URLQueryEncryption&#39;] = true;</span>
<span class="c1">//$cfg[&#39;URLQueryEncryptionSecretKey&#39;] = &#39;&#39;;</span>

<span class="sd">/**</span>
<span class="sd"> * You can find more configuration options in the documentation</span>
<span class="sd"> * in the doc/ folder or at &lt;https://docs.phpmyadmin.net/&gt;.</span>
<span class="sd"> */</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Don’t use the controluser ‘pma’ if it does not yet exist and don’t use ‘pmapass’
as password.</p>
</div>
</div>
<div class="section" id="example-for-signon-authentication">
<span id="example-signon"></span><h3>Example for signon authentication<a class="headerlink" href="#example-for-signon-authentication" title="Permalink to this headline">¶</a></h3>
<p>This example uses <code class="file docutils literal notranslate"><span class="pre">examples/signon.php</span></code> to demonstrate usage of <a class="reference internal" href="setup.html#auth-signon"><span class="std std-ref">Signon authentication mode</span></a>:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;?</span><span class="nx">php</span>
<span class="nv">$i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
<span class="nv">$i</span><span class="o">++</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;auth_type&#39;</span><span class="p">]</span>     <span class="o">=</span> <span class="s1">&#39;signon&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;SignonSession&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;SignonSession&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;SignonURL&#39;</span><span class="p">]</span>     <span class="o">=</span> <span class="s1">&#39;examples/signon.php&#39;</span><span class="p">;</span>
</pre></div>
</div>
</div>
<div class="section" id="example-for-ip-address-limited-autologin">
<h3>Example for IP address limited autologin<a class="headerlink" href="#example-for-ip-address-limited-autologin" title="Permalink to this headline">¶</a></h3>
<p>If you want to automatically login when accessing phpMyAdmin locally while asking
for a password when accessing remotely, you can achieve it using following snippet:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span><span class="nv">$_SERVER</span><span class="p">[</span><span class="s1">&#39;REMOTE_ADDR&#39;</span><span class="p">]</span> <span class="o">===</span> <span class="s1">&#39;127.0.0.1&#39;</span><span class="p">)</span> <span class="p">{</span>
    <span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;auth_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;config&#39;</span><span class="p">;</span>
    <span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;user&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;root&#39;</span><span class="p">;</span>
    <span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;password&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;yourpassword&#39;</span><span class="p">;</span>
<span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
    <span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;auth_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;cookie&#39;</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Filtering based on IP addresses isn’t reliable over the internet, use it
only for local address.</p>
</div>
</div>
<div class="section" id="example-for-using-multiple-mysql-servers">
<h3>Example for using multiple MySQL servers<a class="headerlink" href="#example-for-using-multiple-mysql-servers" title="Permalink to this headline">¶</a></h3>
<p>You can configure any number of servers using <span class="target" id="index-160"></span><a class="reference internal" href="#cfg_Servers"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers']</span></code></a>,
following example shows two of them:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;?</span><span class="nx">php</span>
<span class="c1">// The string is a hexadecimal representation of a 32-bytes long string of random bytes.</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;blowfish_secret&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="nx">sodium_hex2bin</span><span class="p">(</span><span class="s1">&#39;f16ce59f45714194371b48fe362072dc3b019da7861558cd4ad29e4d6fb13851&#39;</span><span class="p">);</span>
<span class="nv">$i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

<span class="nv">$i</span><span class="o">++</span><span class="p">;</span> <span class="c1">// server 1 :</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;auth_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;cookie&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;verbose&#39;</span><span class="p">]</span>   <span class="o">=</span> <span class="s1">&#39;no1&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;host&#39;</span><span class="p">]</span>      <span class="o">=</span> <span class="s1">&#39;localhost&#39;</span><span class="p">;</span>
<span class="c1">// more options for #1 ...</span>

<span class="nv">$i</span><span class="o">++</span><span class="p">;</span> <span class="c1">// server 2 :</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;auth_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;cookie&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;verbose&#39;</span><span class="p">]</span>   <span class="o">=</span> <span class="s1">&#39;no2&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;host&#39;</span><span class="p">]</span>      <span class="o">=</span> <span class="s1">&#39;remote.host.addr&#39;</span><span class="p">;</span><span class="c1">//or ip:&#39;********&#39;</span>
<span class="c1">// this server must allow remote clients, e.g., host 10.9.8.%</span>
<span class="c1">// not only in mysql.host but also in the startup configuration</span>
<span class="c1">// more options for #2 ...</span>

<span class="c1">// end of server sections</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;ServerDefault&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="c1">// to choose the server on startup</span>

<span class="c1">// further general options ...</span>
</pre></div>
</div>
</div>
<div class="section" id="google-cloud-sql-with-ssl">
<span id="example-google-ssl"></span><h3>Google Cloud SQL with SSL<a class="headerlink" href="#google-cloud-sql-with-ssl" title="Permalink to this headline">¶</a></h3>
<p>To connect to Google Could SQL, you currently need to disable certificate
verification. This is caused by the certificate being issued for CN matching
your instance name, but you connect to an IP address and PHP tries to match
these two. With verification you end up with error message like:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Peer certificate CN=`api-project-851612429544:pmatest&#39; did not match expected CN=`*******&#39;
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>With disabled verification your traffic is encrypted, but you’re open to
man in the middle attacks.</p>
</div>
<p>To connect phpMyAdmin to Google Cloud SQL using SSL download the client and
server certificates and tell phpMyAdmin to use them:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="c1">// IP address of your instance</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;host&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;*******&#39;</span><span class="p">;</span>
<span class="c1">// Use SSL for connection</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">true</span><span class="p">;</span>
<span class="c1">// Client secret key</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl_key&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;../client-key.pem&#39;</span><span class="p">;</span>
<span class="c1">// Client certificate</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl_cert&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;../client-cert.pem&#39;</span><span class="p">;</span>
<span class="c1">// Server certification authority</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl_ca&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;../server-ca.pem&#39;</span><span class="p">;</span>
<span class="c1">// Disable SSL verification (see above note)</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl_verify&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">false</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<span class="target" id="index-161"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-162"></span><a class="reference internal" href="#cfg_Servers_ssl_key"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_key']</span></code></a>,
<span class="target" id="index-163"></span><a class="reference internal" href="#cfg_Servers_ssl_cert"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_cert']</span></code></a>,
<span class="target" id="index-164"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-165"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a>,
&lt;<a class="reference external" href="https://bugs.php.net/bug.php?id=72048">https://bugs.php.net/bug.php?id=72048</a>&gt;</p>
</div>
</div>
<div class="section" id="amazon-rds-aurora-with-ssl">
<span id="example-aws-ssl"></span><h3>Amazon RDS Aurora with SSL<a class="headerlink" href="#amazon-rds-aurora-with-ssl" title="Permalink to this headline">¶</a></h3>
<p>To connect phpMyAdmin to an Amazon RDS Aurora MySQL database instance using SSL,
download the CA server certificate and tell phpMyAdmin to use it:</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="c1">// Address of your instance</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;host&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;replace-me-cluster-name.cluster-replace-me-id.replace-me-region.rds.amazonaws.com&#39;</span><span class="p">;</span>
<span class="c1">// Use SSL for connection</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">true</span><span class="p">;</span>
<span class="c1">// You need to have the region CA file and the authority CA file (2019 edition CA for example) in the PEM bundle for it to work</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl_ca&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;../rds-combined-ca-bundle.pem&#39;</span><span class="p">;</span>
<span class="c1">// Enable SSL verification</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;ssl_verify&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">true</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#ssl"><span class="std std-ref">Using SSL for connection to database server</span></a>,
<span class="target" id="index-166"></span><a class="reference internal" href="#cfg_Servers_ssl"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl']</span></code></a>,
<span class="target" id="index-167"></span><a class="reference internal" href="#cfg_Servers_ssl_ca"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_ca']</span></code></a>,
<span class="target" id="index-168"></span><a class="reference internal" href="#cfg_Servers_ssl_verify"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['ssl_verify']</span></code></a></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p>Current RDS CA bundle for all regions <a class="reference external" href="https://s3.amazonaws.com/rds-downloads/rds-combined-ca-bundle.pem">https://s3.amazonaws.com/rds-downloads/rds-combined-ca-bundle.pem</a></p></li>
<li><p>The RDS CA (2019 edition) for the region <cite>eu-west-3</cite> without the parent CA <a class="reference external" href="https://s3.amazonaws.com/rds-downloads/rds-ca-2019-eu-west-3.pem">https://s3.amazonaws.com/rds-downloads/rds-ca-2019-eu-west-3.pem</a></p></li>
<li><p><a class="reference external" href="https://s3.amazonaws.com/rds-downloads/">List of available Amazon RDS CA files</a></p></li>
<li><p><a class="reference external" href="https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/AuroraMySQL.Security.html">Amazon MySQL Aurora security guide</a></p></li>
<li><p><a class="reference external" href="https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/UsingWithRDS.SSL.html">Amazon certificates bundles per region</a></p></li>
</ul>
</div>
</div>
<div class="section" id="recaptcha-using-hcaptcha">
<h3>reCaptcha using hCaptcha<a class="headerlink" href="#recaptcha-using-hcaptcha" title="Permalink to this headline">¶</a></h3>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CaptchaApi&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;https://www.hcaptcha.com/1/api.js&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CaptchaCsp&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;https://hcaptcha.com https://*.hcaptcha.com&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CaptchaRequestParam&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;h-captcha&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CaptchaResponseParam&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;h-captcha-response&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CaptchaSiteVerifyURL&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;https://hcaptcha.com/siteverify&#39;</span><span class="p">;</span>
<span class="c1">// This is the secret key from hCaptcha dashboard</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CaptchaLoginPrivateKey&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;0xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx&#39;</span><span class="p">;</span>
<span class="c1">// This is the site key from hCaptcha dashboard</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;CaptchaLoginPublicKey&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;xxx-xxx-xxx-xxx-xxxx&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://www.hcaptcha.com/">hCaptcha website</a></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://docs.hcaptcha.com/">hCaptcha Developer Guide</a></p>
</div>
</div>
</div>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Configuration</a><ul>
<li><a class="reference internal" href="#basic-settings">Basic settings</a></li>
<li><a class="reference internal" href="#server-connection-settings">Server connection settings</a></li>
<li><a class="reference internal" href="#generic-settings">Generic settings</a></li>
<li><a class="reference internal" href="#cookie-authentication-options">Cookie authentication options</a></li>
<li><a class="reference internal" href="#navigation-panel-setup">Navigation panel setup</a></li>
<li><a class="reference internal" href="#main-panel">Main panel</a></li>
<li><a class="reference internal" href="#database-structure">Database structure</a></li>
<li><a class="reference internal" href="#browse-mode">Browse mode</a></li>
<li><a class="reference internal" href="#editing-mode">Editing mode</a></li>
<li><a class="reference internal" href="#export-and-import-settings">Export and import settings</a></li>
<li><a class="reference internal" href="#tabs-display-settings">Tabs display settings</a></li>
<li><a class="reference internal" href="#pdf-options">PDF Options</a></li>
<li><a class="reference internal" href="#languages">Languages</a></li>
<li><a class="reference internal" href="#web-server-settings">Web server settings</a></li>
<li><a class="reference internal" href="#theme-settings">Theme settings</a></li>
<li><a class="reference internal" href="#design-customization">Design customization</a></li>
<li><a class="reference internal" href="#text-fields">Text fields</a></li>
<li><a class="reference internal" href="#sql-query-box-settings">SQL query box settings</a></li>
<li><a class="reference internal" href="#web-server-upload-save-import-directories">Web server upload/save/import directories</a></li>
<li><a class="reference internal" href="#various-display-setting">Various display setting</a></li>
<li><a class="reference internal" href="#page-titles">Page titles</a></li>
<li><a class="reference internal" href="#theme-manager-settings">Theme manager settings</a></li>
<li><a class="reference internal" href="#default-queries">Default queries</a></li>
<li><a class="reference internal" href="#mysql-settings">MySQL settings</a></li>
<li><a class="reference internal" href="#default-options-for-transformations">Default options for Transformations</a></li>
<li><a class="reference internal" href="#console-settings">Console settings</a></li>
<li><a class="reference internal" href="#developer">Developer</a></li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#basic-example">Basic example</a></li>
<li><a class="reference internal" href="#example-for-signon-authentication">Example for signon authentication</a></li>
<li><a class="reference internal" href="#example-for-ip-address-limited-autologin">Example for IP address limited autologin</a></li>
<li><a class="reference internal" href="#example-for-using-multiple-mysql-servers">Example for using multiple MySQL servers</a></li>
<li><a class="reference internal" href="#google-cloud-sql-with-ssl">Google Cloud SQL with SSL</a></li>
<li><a class="reference internal" href="#amazon-rds-aurora-with-ssl">Amazon RDS Aurora with SSL</a></li>
<li><a class="reference internal" href="#recaptcha-using-hcaptcha">reCaptcha using hCaptcha</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="setup.html"
                        title="previous chapter">Installation</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="user.html"
                        title="next chapter">User Guide</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/config.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="user.html" title="User Guide"
             >next</a> |</li>
        <li class="right" >
          <a href="setup.html" title="Installation"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Configuration</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>