
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Index &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="#" title="General Index"
             accesskey="I">index</a></li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Index</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#Symbols"><strong>Symbols</strong></a>
 | <a href="#A"><strong>A</strong></a>
 | <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#J"><strong>J</strong></a>
 | <a href="#K"><strong>K</strong></a>
 | <a href="#L"><strong>L</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#N"><strong>N</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#Q"><strong>Q</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 | <a href="#V"><strong>V</strong></a>
 | <a href="#W"><strong>W</strong></a>
 | <a href="#X"><strong>X</strong></a>
 | <a href="#Z"><strong>Z</strong></a>
 
</div>
<h2 id="Symbols">Symbols</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_ActionLinksMode"><strong>$cfg[&#39;ActionLinksMode&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_AllowArbitraryServer"><strong>$cfg[&#39;AllowArbitraryServer&#39;]</strong></a>, <a href="config.html#index-141"><strong>[1]</strong></a>, <a href="setup.html#index-0"><strong>[2]</strong></a>, <a href="setup.html#index-33"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_AllowThirdPartyFraming"><strong>$cfg[&#39;AllowThirdPartyFraming&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_AllowUserDropDatabase"><strong>$cfg[&#39;AllowUserDropDatabase&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ArbitraryServerRegexp"><strong>$cfg[&#39;ArbitraryServerRegexp&#39;]</strong></a>, <a href="config.html#index-140"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_AuthLog"><strong>$cfg[&#39;AuthLog&#39;]</strong></a>, <a href="config.html#index-4"><strong>[1]</strong></a>, <a href="setup.html#index-55"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_AuthLogSuccess"><strong>$cfg[&#39;AuthLogSuccess&#39;]</strong></a>, <a href="config.html#index-2"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_AvailableCharsets"><strong>$cfg[&#39;AvailableCharsets&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_blowfish_secret"><strong>$cfg[&#39;blowfish_secret&#39;]</strong></a>, <a href="config.html#index-138"><strong>[1]</strong></a>, <a href="faq.html#index-11"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_BrowseMarkerEnable"><strong>$cfg[&#39;BrowseMarkerEnable&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_BrowseMIME"><strong>$cfg[&#39;BrowseMIME&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_BrowsePointerEnable"><strong>$cfg[&#39;BrowsePointerEnable&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_BZipDump"><strong>$cfg[&#39;BZipDump&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaApi"><strong>$cfg[&#39;CaptchaApi&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaCsp"><strong>$cfg[&#39;CaptchaCsp&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaLoginPrivateKey"><strong>$cfg[&#39;CaptchaLoginPrivateKey&#39;]</strong></a>, <a href="setup.html#index-54"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaLoginPublicKey"><strong>$cfg[&#39;CaptchaLoginPublicKey&#39;]</strong></a>, <a href="setup.html#index-53"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaMethod"><strong>$cfg[&#39;CaptchaMethod&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaRequestParam"><strong>$cfg[&#39;CaptchaRequestParam&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaResponseParam"><strong>$cfg[&#39;CaptchaResponseParam&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaSiteVerifyURL"><strong>$cfg[&#39;CaptchaSiteVerifyURL&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CharEditing"><strong>$cfg[&#39;CharEditing&#39;]</strong></a>, <a href="config.html#index-154"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_CharTextareaCols"><strong>$cfg[&#39;CharTextareaCols&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CharTextareaRows"><strong>$cfg[&#39;CharTextareaRows&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CheckConfigurationPermissions"><strong>$cfg[&#39;CheckConfigurationPermissions&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CodemirrorEnable"><strong>$cfg[&#39;CodemirrorEnable&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CompressOnFly"><strong>$cfg[&#39;CompressOnFly&#39;]</strong></a>, <a href="faq.html#index-12"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Confirm"><strong>$cfg[&#39;Confirm&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Console_AlwaysExpand"><strong>$cfg[&#39;Console&#39;][&#39;AlwaysExpand&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Console_CurrentQuery"><strong>$cfg[&#39;Console&#39;][&#39;CurrentQuery&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Console_DarkTheme"><strong>$cfg[&#39;Console&#39;][&#39;DarkTheme&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Console_EnterExecutes"><strong>$cfg[&#39;Console&#39;][&#39;EnterExecutes&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Console_Height"><strong>$cfg[&#39;Console&#39;][&#39;Height&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Console_Mode"><strong>$cfg[&#39;Console&#39;][&#39;Mode&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Console_StartHistory"><strong>$cfg[&#39;Console&#39;][&#39;StartHistory&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ConsoleEnterExecutes"><strong>$cfg[&#39;ConsoleEnterExecutes&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CookieSameSite"><strong>$cfg[&#39;CookieSameSite&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_CSPAllow"><strong>$cfg[&#39;CSPAllow&#39;]</strong></a>, <a href="config.html#index-146"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_DBG"><strong>$cfg[&#39;DBG&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_demo"><strong>$cfg[&#39;DBG&#39;][&#39;demo&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_simple2fa"><strong>$cfg[&#39;DBG&#39;][&#39;simple2fa&#39;]</strong></a>, <a href="two_factor.html#index-0"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_sql"><strong>$cfg[&#39;DBG&#39;][&#39;sql&#39;]</strong></a>, <a href="config.html#index-159"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_sqllog"><strong>$cfg[&#39;DBG&#39;][&#39;sqllog&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultConnectionCollation"><strong>$cfg[&#39;DefaultConnectionCollation&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultForeignKeyChecks"><strong>$cfg[&#39;DefaultForeignKeyChecks&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultFunctions"><strong>$cfg[&#39;DefaultFunctions&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultLang"><strong>$cfg[&#39;DefaultLang&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultQueryDatabase"><strong>$cfg[&#39;DefaultQueryDatabase&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultQueryTable"><strong>$cfg[&#39;DefaultQueryTable&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTabDatabase"><strong>$cfg[&#39;DefaultTabDatabase&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTabServer"><strong>$cfg[&#39;DefaultTabServer&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTabTable"><strong>$cfg[&#39;DefaultTabTable&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations"><strong>$cfg[&#39;DefaultTransformations&#39;]</strong></a>, <a href="transformations.html#index-0"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Bool2Text"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Bool2Text&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_DateFormat"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;DateFormat&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_External"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;External&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Hex"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Hex&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Inline"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Inline&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_PreApPend"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;PreApPend&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Substring"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Substring&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_TextImageLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextImageLink&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_TextLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextLink&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DisableMultiTableMaintenance"><strong>$cfg[&#39;DisableMultiTableMaintenance&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_DisableShortcutKeys"><strong>$cfg[&#39;DisableShortcutKeys&#39;]</strong></a>, <a href="config.html#index-133"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_DisplayServersList"><strong>$cfg[&#39;DisplayServersList&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_enable_drag_drop_import"><strong>$cfg[&#39;enable_drag_drop_import&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_EnableAutocompleteForTablesAndColumns"><strong>$cfg[&#39;EnableAutocompleteForTablesAndColumns&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_environment"><strong>$cfg[&#39;environment&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ExecTimeLimit"><strong>$cfg[&#39;ExecTimeLimit&#39;]</strong></a>, <a href="faq.html#index-21"><strong>[1]</strong></a>, <a href="setup.html#index-17"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Export"><strong>$cfg[&#39;Export&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Export_charset"><strong>$cfg[&#39;Export&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#index-150"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Export_file_template_database"><strong>$cfg[&#39;Export&#39;][&#39;file_template_database&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Export_file_template_server"><strong>$cfg[&#39;Export&#39;][&#39;file_template_server&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Export_file_template_table"><strong>$cfg[&#39;Export&#39;][&#39;file_template_table&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Export_format"><strong>$cfg[&#39;Export&#39;][&#39;format&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Export_method"><strong>$cfg[&#39;Export&#39;][&#39;method&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Export_remove_definer_from_definitions"><strong>$cfg[&#39;Export&#39;][&#39;remove_definer_from_definitions&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_FilterLanguages"><strong>$cfg[&#39;FilterLanguages&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_FirstDayOfCalendar"><strong>$cfg[&#39;FirstDayOfCalendar&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_FirstLevelNavigationItems"><strong>$cfg[&#39;FirstLevelNavigationItems&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_FontSize"><strong>$cfg[&#39;FontSize&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ForceSSL"><strong>$cfg[&#39;ForceSSL&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ForeignKeyDropdownOrder"><strong>$cfg[&#39;ForeignKeyDropdownOrder&#39;]</strong></a>, <a href="config.html#index-148"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_ForeignKeyMaxLimit"><strong>$cfg[&#39;ForeignKeyMaxLimit&#39;]</strong></a>, <a href="faq.html#index-22"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_GD2Available"><strong>$cfg[&#39;GD2Available&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_GridEditing"><strong>$cfg[&#39;GridEditing&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_GZipDump"><strong>$cfg[&#39;GZipDump&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_HideStructureActions"><strong>$cfg[&#39;HideStructureActions&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_IconvExtraParams"><strong>$cfg[&#39;IconvExtraParams&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_IgnoreMultiSubmitErrors"><strong>$cfg[&#39;IgnoreMultiSubmitErrors&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Import"><strong>$cfg[&#39;Import&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Import_charset"><strong>$cfg[&#39;Import&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#index-151"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_InitialSlidersState"><strong>$cfg[&#39;InitialSlidersState&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_InsertRows"><strong>$cfg[&#39;InsertRows&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Lang"><strong>$cfg[&#39;Lang&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_LimitChars"><strong>$cfg[&#39;LimitChars&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_LinkLengthLimit"><strong>$cfg[&#39;LinkLengthLimit&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieDeleteAll"><strong>$cfg[&#39;LoginCookieDeleteAll&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieRecall"><strong>$cfg[&#39;LoginCookieRecall&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieStore"><strong>$cfg[&#39;LoginCookieStore&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieValidity"><strong>$cfg[&#39;LoginCookieValidity&#39;]</strong></a>, <a href="config.html#index-139"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieValidityDisableWarning"><strong>$cfg[&#39;LoginCookieValidityDisableWarning&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_LongtextDoubleTextarea"><strong>$cfg[&#39;LongtextDoubleTextarea&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxCharactersInDisplayedSQL"><strong>$cfg[&#39;MaxCharactersInDisplayedSQL&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxDbList"><strong>$cfg[&#39;MaxDbList&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxExactCount"><strong>$cfg[&#39;MaxExactCount&#39;]</strong></a>, <a href="faq.html#index-14"><strong>[1]</strong></a>, <a href="faq.html#index-15"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxExactCountViews"><strong>$cfg[&#39;MaxExactCountViews&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxNavigationItems"><strong>$cfg[&#39;MaxNavigationItems&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxRows"><strong>$cfg[&#39;MaxRows&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxSizeForInputField"><strong>$cfg[&#39;MaxSizeForInputField&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MaxTableList"><strong>$cfg[&#39;MaxTableList&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MemoryLimit"><strong>$cfg[&#39;MemoryLimit&#39;]</strong></a>, <a href="setup.html#index-16"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_MinSizeForInputField"><strong>$cfg[&#39;MinSizeForInputField&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MysqlMinVersion"><strong>$cfg[&#39;MysqlMinVersion&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_MysqlSslWarningSafeHosts"><strong>$cfg[&#39;MysqlSslWarningSafeHosts&#39;]</strong></a>, <a href="setup.html#index-63"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_NaturalOrder"><strong>$cfg[&#39;NaturalOrder&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationDisplayLogo"><strong>$cfg[&#39;NavigationDisplayLogo&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationDisplayServers"><strong>$cfg[&#39;NavigationDisplayServers&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationLinkWithMainPanel"><strong>$cfg[&#39;NavigationLinkWithMainPanel&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationLogoLink"><strong>$cfg[&#39;NavigationLogoLink&#39;]</strong></a>, <a href="config.html#index-145"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationLogoLinkWindow"><strong>$cfg[&#39;NavigationLogoLinkWindow&#39;]</strong></a>, <a href="config.html#index-144"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDbSeparator"><strong>$cfg[&#39;NavigationTreeDbSeparator&#39;]</strong></a>, <a href="config.html#index-142"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDefaultTabTable"><strong>$cfg[&#39;NavigationTreeDefaultTabTable&#39;]</strong></a>, <a href="config.html#index-100"><strong>[1]</strong></a>, <a href="config.html#index-97"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDefaultTabTable2"><strong>$cfg[&#39;NavigationTreeDefaultTabTable2&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDisplayDbFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayDbFilterMinimum&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDisplayItemFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayItemFilterMinimum&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeEnableExpansion"><strong>$cfg[&#39;NavigationTreeEnableExpansion&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeEnableGrouping"><strong>$cfg[&#39;NavigationTreeEnableGrouping&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreePointerEnable"><strong>$cfg[&#39;NavigationTreePointerEnable&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowEvents"><strong>$cfg[&#39;NavigationTreeShowEvents&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowFunctions"><strong>$cfg[&#39;NavigationTreeShowFunctions&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowProcedures"><strong>$cfg[&#39;NavigationTreeShowProcedures&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowTables"><strong>$cfg[&#39;NavigationTreeShowTables&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowViews"><strong>$cfg[&#39;NavigationTreeShowViews&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeTableLevel"><strong>$cfg[&#39;NavigationTreeTableLevel&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeTableSeparator"><strong>$cfg[&#39;NavigationTreeTableSeparator&#39;]</strong></a>, <a href="faq.html#index-13"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationWidth"><strong>$cfg[&#39;NavigationWidth&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_NumFavoriteTables"><strong>$cfg[&#39;NumFavoriteTables&#39;]</strong></a>, <a href="config.html#index-101"><strong>[1]</strong></a>, <a href="faq.html#index-26"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_NumRecentTables"><strong>$cfg[&#39;NumRecentTables&#39;]</strong></a>, <a href="config.html#index-96"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_OBGzip"><strong>$cfg[&#39;OBGzip&#39;]</strong></a>, <a href="faq.html#index-0"><strong>[1]</strong></a>, <a href="faq.html#index-3"><strong>[2]</strong></a>, <a href="faq.html#index-8"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_Order"><strong>$cfg[&#39;Order&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_PDFDefaultPageSize"><strong>$cfg[&#39;PDFDefaultPageSize&#39;]</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_PDFPageSizes"><strong>$cfg[&#39;PDFPageSizes&#39;]</strong></a>, <a href="config.html#index-149"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_PersistentConnections"><strong>$cfg[&#39;PersistentConnections&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_PmaAbsoluteUri"><strong>$cfg[&#39;PmaAbsoluteUri&#39;]</strong></a>, <a href="config.html#index-136"><strong>[1]</strong></a>, <a href="faq.html#index-10"><strong>[2]</strong></a>, <a href="faq.html#index-19"><strong>[3]</strong></a>, <a href="faq.html#index-20"><strong>[4]</strong></a>, <a href="faq.html#index-6"><strong>[5]</strong></a>, <a href="faq.html#index-9"><strong>[6]</strong></a>, <a href="setup.html#index-6"><strong>[7]</strong></a>
</li>
      <li><a href="config.html#cfg_PmaNoRelation_DisableWarning"><strong>$cfg[&#39;PmaNoRelation_DisableWarning&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_PropertiesNumColumns"><strong>$cfg[&#39;PropertiesNumColumns&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ProtectBinary"><strong>$cfg[&#39;ProtectBinary&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ProxyPass"><strong>$cfg[&#39;ProxyPass&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ProxyUrl"><strong>$cfg[&#39;ProxyUrl&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ProxyUser"><strong>$cfg[&#39;ProxyUser&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_QueryHistoryDB"><strong>$cfg[&#39;QueryHistoryDB&#39;]</strong></a>, <a href="config.html#index-155"><strong>[1]</strong></a>, <a href="config.html#index-157"><strong>[2]</strong></a>, <a href="setup.html#index-9"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_QueryHistoryMax"><strong>$cfg[&#39;QueryHistoryMax&#39;]</strong></a>, <a href="config.html#index-158"><strong>[1]</strong></a>, <a href="config.html#index-93"><strong>[2]</strong></a>, <a href="setup.html#index-10"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_RecodingEngine"><strong>$cfg[&#39;RecodingEngine&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_RelationalDisplay"><strong>$cfg[&#39;RelationalDisplay&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_RememberSorting"><strong>$cfg[&#39;RememberSorting&#39;]</strong></a>, <a href="config.html#index-104"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_RepeatCells"><strong>$cfg[&#39;RepeatCells&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ReservedWordDisableWarning"><strong>$cfg[&#39;ReservedWordDisableWarning&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_RetainQueryBox"><strong>$cfg[&#39;RetainQueryBox&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_RowActionLinks"><strong>$cfg[&#39;RowActionLinks&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_RowActionLinksWithoutUnique"><strong>$cfg[&#39;RowActionLinksWithoutUnique&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_RowActionType"><strong>$cfg[&#39;RowActionType&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SaveCellsAtOnce"><strong>$cfg[&#39;SaveCellsAtOnce&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SaveDir"><strong>$cfg[&#39;SaveDir&#39;]</strong></a>, <a href="import_export.html#index-1"><strong>[1]</strong></a>, <a href="setup.html#index-19"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_SendErrorReports"><strong>$cfg[&#39;SendErrorReports&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ServerDefault"><strong>$cfg[&#39;ServerDefault&#39;]</strong></a>, <a href="config.html#index-134"><strong>[1]</strong></a>, <a href="config.html#index-135"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_ServerLibraryDifference_DisableWarning"><strong>$cfg[&#39;ServerLibraryDifference_DisableWarning&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers"><strong>$cfg[&#39;Servers&#39;]</strong></a>, <a href="config.html#index-160"><strong>[1]</strong></a>, <a href="config.html#index-6"><strong>[2]</strong></a>, <a href="config.html#index-9"><strong>[3]</strong></a>, <a href="setup.html#index-30"><strong>[4]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowDeny_order"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;]</strong></a>, <a href="config.html#index-152"><strong>[1]</strong></a>, <a href="setup.html#index-48"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowDeny_rules"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;]</strong></a>, <a href="config.html#index-129"><strong>[1]</strong></a>, <a href="config.html#index-153"><strong>[2]</strong></a>, <a href="faq.html#index-18"><strong>[3]</strong></a>, <a href="setup.html#index-49"><strong>[4]</strong></a>, <a href="setup.html#index-51"><strong>[5]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowNoPassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowNoPassword&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowRoot"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowRoot&#39;]</strong></a>, <a href="setup.html#index-52"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_auth_http_realm"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_http_realm&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_auth_swekey_config"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_swekey_config&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_auth_type"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_type&#39;]</strong></a>, <a href="config.html#index-75"><strong>[1]</strong></a>, <a href="setup.html#index-40"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_bookmarktable"><strong>$cfg[&#39;Servers&#39;][$i][&#39;bookmarktable&#39;]</strong></a>, <a href="config.html#index-78"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_central_columns"><strong>$cfg[&#39;Servers&#39;][$i][&#39;central_columns&#39;]</strong></a>, <a href="config.html#index-114"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_column_info"><strong>$cfg[&#39;Servers&#39;][$i][&#39;column_info&#39;]</strong></a>, <a href="config.html#index-90"><strong>[1]</strong></a>, <a href="config.html#index-91"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_compress"><strong>$cfg[&#39;Servers&#39;][$i][&#39;compress&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_connect_type"><strong>$cfg[&#39;Servers&#39;][$i][&#39;connect_type&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_control_*"><strong>$cfg[&#39;Servers&#39;][$i][&#39;control_*&#39;]</strong></a>, <a href="config.html#index-60"><strong>[1]</strong></a>, <a href="config.html#index-61"><strong>[2]</strong></a>, <a href="config.html#index-65"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controlhost"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlhost&#39;]</strong></a>, <a href="config.html#index-63"><strong>[1]</strong></a>, <a href="setup.html#index-11"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controlpass"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlpass&#39;]</strong></a>, <a href="faq.html#index-17"><strong>[1]</strong></a>, <a href="setup.html#index-13"><strong>[2]</strong></a>, <a href="setup.html#index-27"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controlport"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlport&#39;]</strong></a>, <a href="config.html#index-64"><strong>[1]</strong></a>, <a href="setup.html#index-14"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controluser"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controluser&#39;]</strong></a>, <a href="config.html#index-92"><strong>[1]</strong></a>, <a href="faq.html#index-16"><strong>[2]</strong></a>, <a href="setup.html#index-12"><strong>[3]</strong></a>, <a href="setup.html#index-26"><strong>[4]</strong></a>, <a href="setup.html#index-29"><strong>[5]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_designer_coords"><strong>$cfg[&#39;Servers&#39;][$i][&#39;designer_coords&#39;]</strong></a>, <a href="config.html#index-88"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_designer_settings"><strong>$cfg[&#39;Servers&#39;][$i][&#39;designer_settings&#39;]</strong></a>, <a href="config.html#index-116"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_DisableIS"><strong>$cfg[&#39;Servers&#39;][$i][&#39;DisableIS&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_export_templates"><strong>$cfg[&#39;Servers&#39;][$i][&#39;export_templates&#39;]</strong></a>, <a href="config.html#index-120"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_extension"><strong>$cfg[&#39;Servers&#39;][$i][&#39;extension&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_favorite"><strong>$cfg[&#39;Servers&#39;][$i][&#39;favorite&#39;]</strong></a>, <a href="config.html#index-103"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_hide_connection_errors"><strong>$cfg[&#39;Servers&#39;][$i][&#39;hide_connection_errors&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_hide_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;hide_db&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_history"><strong>$cfg[&#39;Servers&#39;][$i][&#39;history&#39;]</strong></a>, <a href="config.html#index-156"><strong>[1]</strong></a>, <a href="config.html#index-95"><strong>[2]</strong></a>, <a href="setup.html#index-8"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_host"><strong>$cfg[&#39;Servers&#39;][$i][&#39;host&#39;]</strong></a>, <a href="config.html#index-12"><strong>[1]</strong></a>, <a href="config.html#index-13"><strong>[2]</strong></a>, <a href="config.html#index-137"><strong>[3]</strong></a>, <a href="config.html#index-14"><strong>[4]</strong></a>, <a href="config.html#index-15"><strong>[5]</strong></a>, <a href="config.html#index-59"><strong>[6]</strong></a>, <a href="config.html#index-7"><strong>[7]</strong></a>, <a href="config.html#index-74"><strong>[8]</strong></a>, <a href="config.html#index-8"><strong>[9]</strong></a>, <a href="setup.html#index-1"><strong>[10]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_LogoutURL"><strong>$cfg[&#39;Servers&#39;][$i][&#39;LogoutURL&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>$cfg[&#39;Servers&#39;][$i][&#39;MaxTableUiprefs&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_navigationhiding"><strong>$cfg[&#39;Servers&#39;][$i][&#39;navigationhiding&#39;]</strong></a>, <a href="config.html#index-112"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_nopassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;nopassword&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_only_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;only_db&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_password"><strong>$cfg[&#39;Servers&#39;][$i][&#39;password&#39;]</strong></a>, <a href="setup.html#index-47"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_pdf_pages"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pdf_pages&#39;]</strong></a>, <a href="config.html#index-83"><strong>[1]</strong></a>, <a href="config.html#index-86"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_pmadb"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pmadb&#39;]</strong></a>, <a href="config.html#index-1"><strong>[1]</strong></a>, <a href="config.html#index-102"><strong>[2]</strong></a>, <a href="config.html#index-105"><strong>[3]</strong></a>, <a href="config.html#index-108"><strong>[4]</strong></a>, <a href="config.html#index-111"><strong>[5]</strong></a>, <a href="config.html#index-113"><strong>[6]</strong></a>, <a href="config.html#index-115"><strong>[7]</strong></a>, <a href="config.html#index-117"><strong>[8]</strong></a>, <a href="config.html#index-119"><strong>[9]</strong></a>, <a href="config.html#index-121"><strong>[10]</strong></a>, <a href="config.html#index-123"><strong>[11]</strong></a>, <a href="config.html#index-124"><strong>[12]</strong></a>, <a href="config.html#index-143"><strong>[13]</strong></a>, <a href="config.html#index-62"><strong>[14]</strong></a>, <a href="config.html#index-76"><strong>[15]</strong></a>, <a href="config.html#index-77"><strong>[16]</strong></a>, <a href="config.html#index-79"><strong>[17]</strong></a>, <a href="config.html#index-81"><strong>[18]</strong></a>, <a href="config.html#index-84"><strong>[19]</strong></a>, <a href="config.html#index-89"><strong>[20]</strong></a>, <a href="config.html#index-94"><strong>[21]</strong></a>, <a href="config.html#index-98"><strong>[22]</strong></a>, <a href="setup.html#index-15"><strong>[23]</strong></a>, <a href="setup.html#index-7"><strong>[24]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_port"><strong>$cfg[&#39;Servers&#39;][$i][&#39;port&#39;]</strong></a>, <a href="config.html#index-11"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_recent"><strong>$cfg[&#39;Servers&#39;][$i][&#39;recent&#39;]</strong></a>, <a href="config.html#index-99"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_relation"><strong>$cfg[&#39;Servers&#39;][$i][&#39;relation&#39;]</strong></a>, <a href="config.html#index-80"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_savedsearches"><strong>$cfg[&#39;Servers&#39;][$i][&#39;savedsearches&#39;]</strong></a>, <a href="config.html#index-118"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SessionTimeZone"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SessionTimeZone&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SignonCookieParams"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonCookieParams&#39;]</strong></a>, <a href="setup.html#index-36"><strong>[1]</strong></a>, <a href="setup.html#index-42"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SignonScript"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonScript&#39;]</strong></a>, <a href="config.html#index-131"><strong>[1]</strong></a>, <a href="config.html#index-132"><strong>[2]</strong></a>, <a href="setup.html#index-37"><strong>[3]</strong></a>, <a href="setup.html#index-39"><strong>[4]</strong></a>, <a href="setup.html#index-43"><strong>[5]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SignonSession"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonSession&#39;]</strong></a>, <a href="setup.html#index-35"><strong>[1]</strong></a>, <a href="setup.html#index-41"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SignonURL"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonURL&#39;]</strong></a>, <a href="setup.html#index-38"><strong>[1]</strong></a>, <a href="setup.html#index-44"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_socket"><strong>$cfg[&#39;Servers&#39;][$i][&#39;socket&#39;]</strong></a>, <a href="config.html#index-10"><strong>[1]</strong></a>, <a href="faq.html#index-7"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl&#39;]</strong></a>, <a href="config.html#index-161"><strong>[1]</strong></a>, <a href="config.html#index-166"><strong>[2]</strong></a>, <a href="config.html#index-22"><strong>[3]</strong></a>, <a href="config.html#index-28"><strong>[4]</strong></a>, <a href="config.html#index-34"><strong>[5]</strong></a>, <a href="config.html#index-40"><strong>[6]</strong></a>, <a href="config.html#index-46"><strong>[7]</strong></a>, <a href="config.html#index-52"><strong>[8]</strong></a>, <a href="config.html#index-66"><strong>[9]</strong></a>, <a href="setup.html#index-57"><strong>[10]</strong></a>, <a href="setup.html#index-64"><strong>[11]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_ca"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca&#39;]</strong></a>, <a href="config.html#index-164"><strong>[1]</strong></a>, <a href="config.html#index-167"><strong>[2]</strong></a>, <a href="config.html#index-18"><strong>[3]</strong></a>, <a href="config.html#index-24"><strong>[4]</strong></a>, <a href="config.html#index-30"><strong>[5]</strong></a>, <a href="config.html#index-43"><strong>[6]</strong></a>, <a href="config.html#index-49"><strong>[7]</strong></a>, <a href="config.html#index-55"><strong>[8]</strong></a>, <a href="config.html#index-69"><strong>[9]</strong></a>, <a href="setup.html#index-60"><strong>[10]</strong></a>, <a href="setup.html#index-67"><strong>[11]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_ca_path"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca_path&#39;]</strong></a>, <a href="config.html#index-19"><strong>[1]</strong></a>, <a href="config.html#index-25"><strong>[2]</strong></a>, <a href="config.html#index-31"><strong>[3]</strong></a>, <a href="config.html#index-37"><strong>[4]</strong></a>, <a href="config.html#index-50"><strong>[5]</strong></a>, <a href="config.html#index-56"><strong>[6]</strong></a>, <a href="config.html#index-70"><strong>[7]</strong></a>, <a href="setup.html#index-61"><strong>[8]</strong></a>, <a href="setup.html#index-68"><strong>[9]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_cert"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_cert&#39;]</strong></a>, <a href="config.html#index-163"><strong>[1]</strong></a>, <a href="config.html#index-17"><strong>[2]</strong></a>, <a href="config.html#index-23"><strong>[3]</strong></a>, <a href="config.html#index-36"><strong>[4]</strong></a>, <a href="config.html#index-42"><strong>[5]</strong></a>, <a href="config.html#index-48"><strong>[6]</strong></a>, <a href="config.html#index-54"><strong>[7]</strong></a>, <a href="config.html#index-68"><strong>[8]</strong></a>, <a href="setup.html#index-59"><strong>[9]</strong></a>, <a href="setup.html#index-66"><strong>[10]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_ciphers"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ciphers&#39;]</strong></a>, <a href="config.html#index-20"><strong>[1]</strong></a>, <a href="config.html#index-26"><strong>[2]</strong></a>, <a href="config.html#index-32"><strong>[3]</strong></a>, <a href="config.html#index-38"><strong>[4]</strong></a>, <a href="config.html#index-44"><strong>[5]</strong></a>, <a href="config.html#index-57"><strong>[6]</strong></a>, <a href="config.html#index-71"><strong>[7]</strong></a>, <a href="setup.html#index-69"><strong>[8]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_key"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_key&#39;]</strong></a>, <a href="config.html#index-16"><strong>[1]</strong></a>, <a href="config.html#index-162"><strong>[2]</strong></a>, <a href="config.html#index-29"><strong>[3]</strong></a>, <a href="config.html#index-35"><strong>[4]</strong></a>, <a href="config.html#index-41"><strong>[5]</strong></a>, <a href="config.html#index-47"><strong>[6]</strong></a>, <a href="config.html#index-53"><strong>[7]</strong></a>, <a href="config.html#index-67"><strong>[8]</strong></a>, <a href="setup.html#index-58"><strong>[9]</strong></a>, <a href="setup.html#index-65"><strong>[10]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_verify"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_verify&#39;]</strong></a>, <a href="config.html#index-165"><strong>[1]</strong></a>, <a href="config.html#index-168"><strong>[2]</strong></a>, <a href="config.html#index-21"><strong>[3]</strong></a>, <a href="config.html#index-27"><strong>[4]</strong></a>, <a href="config.html#index-33"><strong>[5]</strong></a>, <a href="config.html#index-39"><strong>[6]</strong></a>, <a href="config.html#index-45"><strong>[7]</strong></a>, <a href="config.html#index-51"><strong>[8]</strong></a>, <a href="config.html#index-58"><strong>[9]</strong></a>, <a href="config.html#index-72"><strong>[10]</strong></a>, <a href="setup.html#index-62"><strong>[11]</strong></a>, <a href="setup.html#index-70"><strong>[12]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_table_coords"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_coords&#39;]</strong></a>, <a href="config.html#index-85"><strong>[1]</strong></a>, <a href="config.html#index-87"><strong>[2]</strong></a>, <a href="relations.html#index-0"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_table_info"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_info&#39;]</strong></a>, <a href="config.html#index-82"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_table_uiprefs"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_uiprefs&#39;]</strong></a>, <a href="config.html#index-106"><strong>[1]</strong></a>, <a href="config.html#index-126"><strong>[2]</strong></a>, <a href="config.html#index-127"><strong>[3]</strong></a>, <a href="config.html#index-128"><strong>[4]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking&#39;]</strong></a>, <a href="config.html#index-122"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_database&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_table&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_view&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_default_statements"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_default_statements&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_version_auto_create&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_user"><strong>$cfg[&#39;Servers&#39;][$i][&#39;user&#39;]</strong></a>, <a href="setup.html#index-46"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_userconfig"><strong>$cfg[&#39;Servers&#39;][$i][&#39;userconfig&#39;]</strong></a>, <a href="config.html#index-125"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_usergroups"><strong>$cfg[&#39;Servers&#39;][$i][&#39;usergroups&#39;]</strong></a>, <a href="config.html#index-107"><strong>[1]</strong></a>, <a href="config.html#index-110"><strong>[2]</strong></a>, <a href="privileges.html#index-1"><strong>[3]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_users"><strong>$cfg[&#39;Servers&#39;][$i][&#39;users&#39;]</strong></a>, <a href="config.html#index-109"><strong>[1]</strong></a>, <a href="privileges.html#index-0"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_verbose"><strong>$cfg[&#39;Servers&#39;][$i][&#39;verbose&#39;]</strong></a>, <a href="config.html#index-147"><strong>[1]</strong></a>, <a href="config.html#index-73"><strong>[2]</strong></a>, <a href="faq.html#index-23"><strong>[3]</strong></a>, <a href="setup.html#index-3"><strong>[4]</strong></a>
</li>
      <li><a href="config.html#cfg_SessionSavePath"><strong>$cfg[&#39;SessionSavePath&#39;]</strong></a>, <a href="setup.html#index-56"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowAll"><strong>$cfg[&#39;ShowAll&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowBrowseComments"><strong>$cfg[&#39;ShowBrowseComments&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowChgPassword"><strong>$cfg[&#39;ShowChgPassword&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowColumnComments"><strong>$cfg[&#39;ShowColumnComments&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowCreateDb"><strong>$cfg[&#39;ShowCreateDb&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDatabasesNavigationAsTree"><strong>$cfg[&#39;ShowDatabasesNavigationAsTree&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDbStructureCreation"><strong>$cfg[&#39;ShowDbStructureCreation&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDbStructureLastCheck"><strong>$cfg[&#39;ShowDbStructureLastCheck&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDbStructureLastUpdate"><strong>$cfg[&#39;ShowDbStructureLastUpdate&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowFieldTypesInDataEditView"><strong>$cfg[&#39;ShowFieldTypesInDataEditView&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowFunctionFields"><strong>$cfg[&#39;ShowFunctionFields&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowGitRevision"><strong>$cfg[&#39;ShowGitRevision&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowHint"><strong>$cfg[&#39;ShowHint&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowPhpInfo"><strong>$cfg[&#39;ShowPhpInfo&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowPropertyComments"><strong>$cfg[&#39;ShowPropertyComments&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowServerInfo"><strong>$cfg[&#39;ShowServerInfo&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowSQL"><strong>$cfg[&#39;ShowSQL&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ShowStats"><strong>$cfg[&#39;ShowStats&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SkipLockedTables"><strong>$cfg[&#39;SkipLockedTables&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_Edit"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Edit&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_Explain"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Explain&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_Refresh"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Refresh&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_ShowAsPHP"><strong>$cfg[&#39;SQLQuery&#39;][&#39;ShowAsPHP&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_SuhosinDisableWarning"><strong>$cfg[&#39;SuhosinDisableWarning&#39;]</strong></a>, <a href="faq.html#index-5"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_TableNavigationLinksMode"><strong>$cfg[&#39;TableNavigationLinksMode&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TablePrimaryKeyOrder"><strong>$cfg[&#39;TablePrimaryKeyOrder&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TabsMode"><strong>$cfg[&#39;TabsMode&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TempDir"><strong>$cfg[&#39;TempDir&#39;]</strong></a>, <a href="config.html#index-3"><strong>[1]</strong></a>, <a href="faq.html#index-1"><strong>[2]</strong></a>, <a href="faq.html#index-25"><strong>[3]</strong></a>, <a href="setup.html#index-50"><strong>[4]</strong></a>
</li>
      <li><a href="config.html#cfg_TextareaAutoSelect"><strong>$cfg[&#39;TextareaAutoSelect&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TextareaCols"><strong>$cfg[&#39;TextareaCols&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TextareaRows"><strong>$cfg[&#39;TextareaRows&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ThemeDefault"><strong>$cfg[&#39;ThemeDefault&#39;]</strong></a>, <a href="themes.html#index-1"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_ThemeManager"><strong>$cfg[&#39;ThemeManager&#39;]</strong></a>, <a href="themes.html#index-0"><strong>[1]</strong></a>, <a href="themes.html#index-2"><strong>[2]</strong></a>
</li>
      <li><a href="config.html#cfg_ThemePerServer"><strong>$cfg[&#39;ThemePerServer&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TitleDatabase"><strong>$cfg[&#39;TitleDatabase&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TitleDefault"><strong>$cfg[&#39;TitleDefault&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TitleServer"><strong>$cfg[&#39;TitleServer&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TitleTable"><strong>$cfg[&#39;TitleTable&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TranslationWarningThreshold"><strong>$cfg[&#39;TranslationWarningThreshold&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_TrustedProxies"><strong>$cfg[&#39;TrustedProxies&#39;]</strong></a>, <a href="config.html#index-130"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_UploadDir"><strong>$cfg[&#39;UploadDir&#39;]</strong></a>, <a href="faq.html#index-2"><strong>[1]</strong></a>, <a href="faq.html#index-24"><strong>[2]</strong></a>, <a href="import_export.html#index-0"><strong>[3]</strong></a>, <a href="setup.html#index-18"><strong>[4]</strong></a>
</li>
      <li><a href="config.html#cfg_URLQueryEncryption"><strong>$cfg[&#39;URLQueryEncryption&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_URLQueryEncryptionSecretKey"><strong>$cfg[&#39;URLQueryEncryptionSecretKey&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_UseDbSearch"><strong>$cfg[&#39;UseDbSearch&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_UserprefsDeveloperTab"><strong>$cfg[&#39;UserprefsDeveloperTab&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_UserprefsDisallow"><strong>$cfg[&#39;UserprefsDisallow&#39;]</strong></a>, <a href="config.html#index-5"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_VersionCheck"><strong>$cfg[&#39;VersionCheck&#39;]</strong></a>
</li>
      <li><a href="config.html#cfg_ZeroConf"><strong>$cfg[&#39;ZeroConf&#39;]</strong></a>, <a href="setup.html#index-25"><strong>[1]</strong></a>
</li>
      <li><a href="config.html#cfg_ZipDump"><strong>$cfg[&#39;ZipDump&#39;]</strong></a>
</li>
      <li><a href="glossary.html#term-.htaccess"><strong>.htaccess</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-ACL"><strong>ACL</strong></a>
</li>
      <li><a href="config.html#cfg_ActionLinksMode"><strong>ActionLinksMode</strong></a>
</li>
      <li><a href="config.html#cfg_AllowArbitraryServer"><strong>AllowArbitraryServer</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowDeny_order"><strong>AllowDeny, order</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowDeny_rules"><strong>AllowDeny, rules</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowNoPassword"><strong>AllowNoPassword</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_AllowRoot"><strong>AllowRoot</strong></a>
</li>
      <li><a href="config.html#cfg_AllowThirdPartyFraming"><strong>AllowThirdPartyFraming</strong></a>
</li>
      <li><a href="config.html#cfg_AllowUserDropDatabase"><strong>AllowUserDropDatabase</strong></a>
</li>
      <li><a href="config.html#cfg_ArbitraryServerRegexp"><strong>ArbitraryServerRegexp</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Servers_auth_http_realm"><strong>auth_http_realm</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_auth_swekey_config"><strong>auth_swekey_config</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_auth_type"><strong>auth_type</strong></a>
</li>
      <li><a href="setup.html#index-28">Authentication mode</a>

      <ul>
        <li><a href="setup.html#index-45">Config</a>
</li>
        <li><a href="setup.html#index-32">Cookie</a>
</li>
        <li><a href="setup.html#index-31">HTTP</a>
</li>
        <li><a href="setup.html#index-34">Signon</a>
</li>
      </ul></li>
      <li><a href="config.html#cfg_AuthLog"><strong>AuthLog</strong></a>
</li>
      <li><a href="config.html#cfg_AuthLogSuccess"><strong>AuthLogSuccess</strong></a>
</li>
      <li><a href="config.html#cfg_AvailableCharsets"><strong>AvailableCharsets</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Blowfish"><strong>Blowfish</strong></a>
</li>
      <li><a href="config.html#cfg_blowfish_secret"><strong>blowfish_secret</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_bookmarktable"><strong>bookmarktable</strong></a>
</li>
      <li><a href="config.html#cfg_BrowseMarkerEnable"><strong>BrowseMarkerEnable</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_BrowseMIME"><strong>BrowseMIME</strong></a>
</li>
      <li><a href="config.html#cfg_BrowsePointerEnable"><strong>BrowsePointerEnable</strong></a>
</li>
      <li><a href="glossary.html#term-Browser"><strong>Browser</strong></a>
</li>
      <li><a href="glossary.html#term-bzip2"><strong>bzip2</strong></a>
</li>
      <li><a href="config.html#cfg_BZipDump"><strong>BZipDump</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_CaptchaApi"><strong>CaptchaApi</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaCsp"><strong>CaptchaCsp</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaLoginPrivateKey"><strong>CaptchaLoginPrivateKey</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaLoginPublicKey"><strong>CaptchaLoginPublicKey</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaMethod"><strong>CaptchaMethod</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaRequestParam"><strong>CaptchaRequestParam</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaResponseParam"><strong>CaptchaResponseParam</strong></a>
</li>
      <li><a href="config.html#cfg_CaptchaSiteVerifyURL"><strong>CaptchaSiteVerifyURL</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_central_columns"><strong>central_columns</strong></a>
</li>
      <li><a href="glossary.html#term-CGI"><strong>CGI</strong></a>
</li>
      <li><a href="glossary.html#term-Changelog"><strong>Changelog</strong></a>
</li>
      <li><a href="config.html#cfg_CharEditing"><strong>CharEditing</strong></a>
</li>
      <li><a href="config.html#cfg_CharTextareaCols"><strong>CharTextareaCols</strong></a>
</li>
      <li><a href="config.html#cfg_CharTextareaRows"><strong>CharTextareaRows</strong></a>
</li>
      <li><a href="config.html#cfg_CheckConfigurationPermissions"><strong>CheckConfigurationPermissions</strong></a>
</li>
      <li><a href="glossary.html#term-Client"><strong>Client</strong></a>
</li>
      <li><a href="config.html#cfg_CodemirrorEnable"><strong>CodemirrorEnable</strong></a>
</li>
      <li><a href="glossary.html#term-column"><strong>column</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_column_info"><strong>column_info</strong></a>
</li>
      <li><a href="import_export.html#comment">comment (global variable or constant)</a>
</li>
      <li><a href="config.html#cfg_Servers_compress"><strong>compress</strong></a>
</li>
      <li><a href="config.html#cfg_CompressOnFly"><strong>CompressOnFly</strong></a>
</li>
      <li>
    Config

      <ul>
        <li><a href="setup.html#index-45">Authentication mode</a>
</li>
      </ul></li>
      <li><a href="config.html#index-0">config.inc.php</a>
</li>
      <li>
    configuration option

      <ul>
        <li><a href="config.html#cfg_ActionLinksMode"><strong>$cfg[&#39;ActionLinksMode&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_AllowArbitraryServer"><strong>$cfg[&#39;AllowArbitraryServer&#39;]</strong></a>, <a href="config.html#index-141"><strong>[1]</strong></a>, <a href="setup.html#index-0"><strong>[2]</strong></a>, <a href="setup.html#index-33"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_AllowThirdPartyFraming"><strong>$cfg[&#39;AllowThirdPartyFraming&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_AllowUserDropDatabase"><strong>$cfg[&#39;AllowUserDropDatabase&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ArbitraryServerRegexp"><strong>$cfg[&#39;ArbitraryServerRegexp&#39;]</strong></a>, <a href="config.html#index-140"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_AuthLog"><strong>$cfg[&#39;AuthLog&#39;]</strong></a>, <a href="config.html#index-4"><strong>[1]</strong></a>, <a href="setup.html#index-55"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_AuthLogSuccess"><strong>$cfg[&#39;AuthLogSuccess&#39;]</strong></a>, <a href="config.html#index-2"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_AvailableCharsets"><strong>$cfg[&#39;AvailableCharsets&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_blowfish_secret"><strong>$cfg[&#39;blowfish_secret&#39;]</strong></a>, <a href="config.html#index-138"><strong>[1]</strong></a>, <a href="faq.html#index-11"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_BrowseMarkerEnable"><strong>$cfg[&#39;BrowseMarkerEnable&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_BrowseMIME"><strong>$cfg[&#39;BrowseMIME&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_BrowsePointerEnable"><strong>$cfg[&#39;BrowsePointerEnable&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_BZipDump"><strong>$cfg[&#39;BZipDump&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaApi"><strong>$cfg[&#39;CaptchaApi&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaCsp"><strong>$cfg[&#39;CaptchaCsp&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaLoginPrivateKey"><strong>$cfg[&#39;CaptchaLoginPrivateKey&#39;]</strong></a>, <a href="setup.html#index-54"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaLoginPublicKey"><strong>$cfg[&#39;CaptchaLoginPublicKey&#39;]</strong></a>, <a href="setup.html#index-53"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaMethod"><strong>$cfg[&#39;CaptchaMethod&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaRequestParam"><strong>$cfg[&#39;CaptchaRequestParam&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaResponseParam"><strong>$cfg[&#39;CaptchaResponseParam&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CaptchaSiteVerifyURL"><strong>$cfg[&#39;CaptchaSiteVerifyURL&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CharEditing"><strong>$cfg[&#39;CharEditing&#39;]</strong></a>, <a href="config.html#index-154"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_CharTextareaCols"><strong>$cfg[&#39;CharTextareaCols&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CharTextareaRows"><strong>$cfg[&#39;CharTextareaRows&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CheckConfigurationPermissions"><strong>$cfg[&#39;CheckConfigurationPermissions&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CodemirrorEnable"><strong>$cfg[&#39;CodemirrorEnable&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CompressOnFly"><strong>$cfg[&#39;CompressOnFly&#39;]</strong></a>, <a href="faq.html#index-12"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Confirm"><strong>$cfg[&#39;Confirm&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Console_AlwaysExpand"><strong>$cfg[&#39;Console&#39;][&#39;AlwaysExpand&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Console_CurrentQuery"><strong>$cfg[&#39;Console&#39;][&#39;CurrentQuery&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Console_DarkTheme"><strong>$cfg[&#39;Console&#39;][&#39;DarkTheme&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Console_EnterExecutes"><strong>$cfg[&#39;Console&#39;][&#39;EnterExecutes&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Console_Height"><strong>$cfg[&#39;Console&#39;][&#39;Height&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Console_Mode"><strong>$cfg[&#39;Console&#39;][&#39;Mode&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Console_StartHistory"><strong>$cfg[&#39;Console&#39;][&#39;StartHistory&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ConsoleEnterExecutes"><strong>$cfg[&#39;ConsoleEnterExecutes&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CookieSameSite"><strong>$cfg[&#39;CookieSameSite&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_CSPAllow"><strong>$cfg[&#39;CSPAllow&#39;]</strong></a>, <a href="config.html#index-146"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_DBG"><strong>$cfg[&#39;DBG&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DBG_demo"><strong>$cfg[&#39;DBG&#39;][&#39;demo&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DBG_simple2fa"><strong>$cfg[&#39;DBG&#39;][&#39;simple2fa&#39;]</strong></a>, <a href="two_factor.html#index-0"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_DBG_sql"><strong>$cfg[&#39;DBG&#39;][&#39;sql&#39;]</strong></a>, <a href="config.html#index-159"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_DBG_sqllog"><strong>$cfg[&#39;DBG&#39;][&#39;sqllog&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultConnectionCollation"><strong>$cfg[&#39;DefaultConnectionCollation&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultForeignKeyChecks"><strong>$cfg[&#39;DefaultForeignKeyChecks&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultFunctions"><strong>$cfg[&#39;DefaultFunctions&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultLang"><strong>$cfg[&#39;DefaultLang&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultQueryDatabase"><strong>$cfg[&#39;DefaultQueryDatabase&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultQueryTable"><strong>$cfg[&#39;DefaultQueryTable&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTabDatabase"><strong>$cfg[&#39;DefaultTabDatabase&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTabServer"><strong>$cfg[&#39;DefaultTabServer&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTabTable"><strong>$cfg[&#39;DefaultTabTable&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations"><strong>$cfg[&#39;DefaultTransformations&#39;]</strong></a>, <a href="transformations.html#index-0"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_Bool2Text"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Bool2Text&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_DateFormat"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;DateFormat&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_External"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;External&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_Hex"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Hex&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_Inline"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Inline&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_PreApPend"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;PreApPend&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_Substring"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;Substring&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_TextImageLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextImageLink&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DefaultTransformations_TextLink"><strong>$cfg[&#39;DefaultTransformations&#39;][&#39;TextLink&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DisableMultiTableMaintenance"><strong>$cfg[&#39;DisableMultiTableMaintenance&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_DisableShortcutKeys"><strong>$cfg[&#39;DisableShortcutKeys&#39;]</strong></a>, <a href="config.html#index-133"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_DisplayServersList"><strong>$cfg[&#39;DisplayServersList&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_enable_drag_drop_import"><strong>$cfg[&#39;enable_drag_drop_import&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_EnableAutocompleteForTablesAndColumns"><strong>$cfg[&#39;EnableAutocompleteForTablesAndColumns&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_environment"><strong>$cfg[&#39;environment&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ExecTimeLimit"><strong>$cfg[&#39;ExecTimeLimit&#39;]</strong></a>, <a href="faq.html#index-21"><strong>[1]</strong></a>, <a href="setup.html#index-17"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Export"><strong>$cfg[&#39;Export&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Export_charset"><strong>$cfg[&#39;Export&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#index-150"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Export_file_template_database"><strong>$cfg[&#39;Export&#39;][&#39;file_template_database&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Export_file_template_server"><strong>$cfg[&#39;Export&#39;][&#39;file_template_server&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Export_file_template_table"><strong>$cfg[&#39;Export&#39;][&#39;file_template_table&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Export_format"><strong>$cfg[&#39;Export&#39;][&#39;format&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Export_method"><strong>$cfg[&#39;Export&#39;][&#39;method&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Export_remove_definer_from_definitions"><strong>$cfg[&#39;Export&#39;][&#39;remove_definer_from_definitions&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_FilterLanguages"><strong>$cfg[&#39;FilterLanguages&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_FirstDayOfCalendar"><strong>$cfg[&#39;FirstDayOfCalendar&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_FirstLevelNavigationItems"><strong>$cfg[&#39;FirstLevelNavigationItems&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_FontSize"><strong>$cfg[&#39;FontSize&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ForceSSL"><strong>$cfg[&#39;ForceSSL&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ForeignKeyDropdownOrder"><strong>$cfg[&#39;ForeignKeyDropdownOrder&#39;]</strong></a>, <a href="config.html#index-148"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_ForeignKeyMaxLimit"><strong>$cfg[&#39;ForeignKeyMaxLimit&#39;]</strong></a>, <a href="faq.html#index-22"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_GD2Available"><strong>$cfg[&#39;GD2Available&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_GridEditing"><strong>$cfg[&#39;GridEditing&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_GZipDump"><strong>$cfg[&#39;GZipDump&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_HideStructureActions"><strong>$cfg[&#39;HideStructureActions&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_IconvExtraParams"><strong>$cfg[&#39;IconvExtraParams&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_IgnoreMultiSubmitErrors"><strong>$cfg[&#39;IgnoreMultiSubmitErrors&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Import"><strong>$cfg[&#39;Import&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Import_charset"><strong>$cfg[&#39;Import&#39;][&#39;charset&#39;]</strong></a>, <a href="config.html#index-151"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_InitialSlidersState"><strong>$cfg[&#39;InitialSlidersState&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_InsertRows"><strong>$cfg[&#39;InsertRows&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Lang"><strong>$cfg[&#39;Lang&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_LimitChars"><strong>$cfg[&#39;LimitChars&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_LinkLengthLimit"><strong>$cfg[&#39;LinkLengthLimit&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_LoginCookieDeleteAll"><strong>$cfg[&#39;LoginCookieDeleteAll&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_LoginCookieRecall"><strong>$cfg[&#39;LoginCookieRecall&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_LoginCookieStore"><strong>$cfg[&#39;LoginCookieStore&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_LoginCookieValidity"><strong>$cfg[&#39;LoginCookieValidity&#39;]</strong></a>, <a href="config.html#index-139"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_LoginCookieValidityDisableWarning"><strong>$cfg[&#39;LoginCookieValidityDisableWarning&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_LongtextDoubleTextarea"><strong>$cfg[&#39;LongtextDoubleTextarea&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxCharactersInDisplayedSQL"><strong>$cfg[&#39;MaxCharactersInDisplayedSQL&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxDbList"><strong>$cfg[&#39;MaxDbList&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxExactCount"><strong>$cfg[&#39;MaxExactCount&#39;]</strong></a>, <a href="faq.html#index-14"><strong>[1]</strong></a>, <a href="faq.html#index-15"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxExactCountViews"><strong>$cfg[&#39;MaxExactCountViews&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxNavigationItems"><strong>$cfg[&#39;MaxNavigationItems&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxRows"><strong>$cfg[&#39;MaxRows&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxSizeForInputField"><strong>$cfg[&#39;MaxSizeForInputField&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MaxTableList"><strong>$cfg[&#39;MaxTableList&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MemoryLimit"><strong>$cfg[&#39;MemoryLimit&#39;]</strong></a>, <a href="setup.html#index-16"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_MinSizeForInputField"><strong>$cfg[&#39;MinSizeForInputField&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MysqlMinVersion"><strong>$cfg[&#39;MysqlMinVersion&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_MysqlSslWarningSafeHosts"><strong>$cfg[&#39;MysqlSslWarningSafeHosts&#39;]</strong></a>, <a href="setup.html#index-63"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_NaturalOrder"><strong>$cfg[&#39;NaturalOrder&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationDisplayLogo"><strong>$cfg[&#39;NavigationDisplayLogo&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationDisplayServers"><strong>$cfg[&#39;NavigationDisplayServers&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationLinkWithMainPanel"><strong>$cfg[&#39;NavigationLinkWithMainPanel&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationLogoLink"><strong>$cfg[&#39;NavigationLogoLink&#39;]</strong></a>, <a href="config.html#index-145"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationLogoLinkWindow"><strong>$cfg[&#39;NavigationLogoLinkWindow&#39;]</strong></a>, <a href="config.html#index-144"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeDbSeparator"><strong>$cfg[&#39;NavigationTreeDbSeparator&#39;]</strong></a>, <a href="config.html#index-142"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeDefaultTabTable"><strong>$cfg[&#39;NavigationTreeDefaultTabTable&#39;]</strong></a>, <a href="config.html#index-100"><strong>[1]</strong></a>, <a href="config.html#index-97"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeDefaultTabTable2"><strong>$cfg[&#39;NavigationTreeDefaultTabTable2&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeDisplayDbFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayDbFilterMinimum&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeDisplayItemFilterMinimum"><strong>$cfg[&#39;NavigationTreeDisplayItemFilterMinimum&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeEnableExpansion"><strong>$cfg[&#39;NavigationTreeEnableExpansion&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeEnableGrouping"><strong>$cfg[&#39;NavigationTreeEnableGrouping&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreePointerEnable"><strong>$cfg[&#39;NavigationTreePointerEnable&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeShowEvents"><strong>$cfg[&#39;NavigationTreeShowEvents&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeShowFunctions"><strong>$cfg[&#39;NavigationTreeShowFunctions&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeShowProcedures"><strong>$cfg[&#39;NavigationTreeShowProcedures&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeShowTables"><strong>$cfg[&#39;NavigationTreeShowTables&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeShowViews"><strong>$cfg[&#39;NavigationTreeShowViews&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeTableLevel"><strong>$cfg[&#39;NavigationTreeTableLevel&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationTreeTableSeparator"><strong>$cfg[&#39;NavigationTreeTableSeparator&#39;]</strong></a>, <a href="faq.html#index-13"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_NavigationWidth"><strong>$cfg[&#39;NavigationWidth&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_NumFavoriteTables"><strong>$cfg[&#39;NumFavoriteTables&#39;]</strong></a>, <a href="config.html#index-101"><strong>[1]</strong></a>, <a href="faq.html#index-26"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_NumRecentTables"><strong>$cfg[&#39;NumRecentTables&#39;]</strong></a>, <a href="config.html#index-96"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_OBGzip"><strong>$cfg[&#39;OBGzip&#39;]</strong></a>, <a href="faq.html#index-0"><strong>[1]</strong></a>, <a href="faq.html#index-3"><strong>[2]</strong></a>, <a href="faq.html#index-8"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_Order"><strong>$cfg[&#39;Order&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_PDFDefaultPageSize"><strong>$cfg[&#39;PDFDefaultPageSize&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_PDFPageSizes"><strong>$cfg[&#39;PDFPageSizes&#39;]</strong></a>, <a href="config.html#index-149"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_PersistentConnections"><strong>$cfg[&#39;PersistentConnections&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_PmaAbsoluteUri"><strong>$cfg[&#39;PmaAbsoluteUri&#39;]</strong></a>, <a href="config.html#index-136"><strong>[1]</strong></a>, <a href="faq.html#index-10"><strong>[2]</strong></a>, <a href="faq.html#index-19"><strong>[3]</strong></a>, <a href="faq.html#index-20"><strong>[4]</strong></a>, <a href="faq.html#index-6"><strong>[5]</strong></a>, <a href="faq.html#index-9"><strong>[6]</strong></a>, <a href="setup.html#index-6"><strong>[7]</strong></a>
</li>
        <li><a href="config.html#cfg_PmaNoRelation_DisableWarning"><strong>$cfg[&#39;PmaNoRelation_DisableWarning&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_PropertiesNumColumns"><strong>$cfg[&#39;PropertiesNumColumns&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ProtectBinary"><strong>$cfg[&#39;ProtectBinary&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ProxyPass"><strong>$cfg[&#39;ProxyPass&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ProxyUrl"><strong>$cfg[&#39;ProxyUrl&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ProxyUser"><strong>$cfg[&#39;ProxyUser&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_QueryHistoryDB"><strong>$cfg[&#39;QueryHistoryDB&#39;]</strong></a>, <a href="config.html#index-155"><strong>[1]</strong></a>, <a href="config.html#index-157"><strong>[2]</strong></a>, <a href="setup.html#index-9"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_QueryHistoryMax"><strong>$cfg[&#39;QueryHistoryMax&#39;]</strong></a>, <a href="config.html#index-158"><strong>[1]</strong></a>, <a href="config.html#index-93"><strong>[2]</strong></a>, <a href="setup.html#index-10"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_RecodingEngine"><strong>$cfg[&#39;RecodingEngine&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_RelationalDisplay"><strong>$cfg[&#39;RelationalDisplay&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_RememberSorting"><strong>$cfg[&#39;RememberSorting&#39;]</strong></a>, <a href="config.html#index-104"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_RepeatCells"><strong>$cfg[&#39;RepeatCells&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ReservedWordDisableWarning"><strong>$cfg[&#39;ReservedWordDisableWarning&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_RetainQueryBox"><strong>$cfg[&#39;RetainQueryBox&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_RowActionLinks"><strong>$cfg[&#39;RowActionLinks&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_RowActionLinksWithoutUnique"><strong>$cfg[&#39;RowActionLinksWithoutUnique&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_RowActionType"><strong>$cfg[&#39;RowActionType&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SaveCellsAtOnce"><strong>$cfg[&#39;SaveCellsAtOnce&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SaveDir"><strong>$cfg[&#39;SaveDir&#39;]</strong></a>, <a href="import_export.html#index-1"><strong>[1]</strong></a>, <a href="setup.html#index-19"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_SendErrorReports"><strong>$cfg[&#39;SendErrorReports&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ServerDefault"><strong>$cfg[&#39;ServerDefault&#39;]</strong></a>, <a href="config.html#index-134"><strong>[1]</strong></a>, <a href="config.html#index-135"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_ServerLibraryDifference_DisableWarning"><strong>$cfg[&#39;ServerLibraryDifference_DisableWarning&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers"><strong>$cfg[&#39;Servers&#39;]</strong></a>, <a href="config.html#index-160"><strong>[1]</strong></a>, <a href="config.html#index-6"><strong>[2]</strong></a>, <a href="config.html#index-9"><strong>[3]</strong></a>, <a href="setup.html#index-30"><strong>[4]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_AllowDeny_order"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;order&#39;]</strong></a>, <a href="config.html#index-152"><strong>[1]</strong></a>, <a href="setup.html#index-48"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_AllowDeny_rules"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowDeny&#39;][&#39;rules&#39;]</strong></a>, <a href="config.html#index-129"><strong>[1]</strong></a>, <a href="config.html#index-153"><strong>[2]</strong></a>, <a href="faq.html#index-18"><strong>[3]</strong></a>, <a href="setup.html#index-49"><strong>[4]</strong></a>, <a href="setup.html#index-51"><strong>[5]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_AllowNoPassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowNoPassword&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_AllowRoot"><strong>$cfg[&#39;Servers&#39;][$i][&#39;AllowRoot&#39;]</strong></a>, <a href="setup.html#index-52"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_auth_http_realm"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_http_realm&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_auth_swekey_config"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_swekey_config&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_auth_type"><strong>$cfg[&#39;Servers&#39;][$i][&#39;auth_type&#39;]</strong></a>, <a href="config.html#index-75"><strong>[1]</strong></a>, <a href="setup.html#index-40"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_bookmarktable"><strong>$cfg[&#39;Servers&#39;][$i][&#39;bookmarktable&#39;]</strong></a>, <a href="config.html#index-78"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_central_columns"><strong>$cfg[&#39;Servers&#39;][$i][&#39;central_columns&#39;]</strong></a>, <a href="config.html#index-114"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_column_info"><strong>$cfg[&#39;Servers&#39;][$i][&#39;column_info&#39;]</strong></a>, <a href="config.html#index-90"><strong>[1]</strong></a>, <a href="config.html#index-91"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_compress"><strong>$cfg[&#39;Servers&#39;][$i][&#39;compress&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_connect_type"><strong>$cfg[&#39;Servers&#39;][$i][&#39;connect_type&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_control_*"><strong>$cfg[&#39;Servers&#39;][$i][&#39;control_*&#39;]</strong></a>, <a href="config.html#index-60"><strong>[1]</strong></a>, <a href="config.html#index-61"><strong>[2]</strong></a>, <a href="config.html#index-65"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controlhost"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlhost&#39;]</strong></a>, <a href="config.html#index-63"><strong>[1]</strong></a>, <a href="setup.html#index-11"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controlpass"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlpass&#39;]</strong></a>, <a href="faq.html#index-17"><strong>[1]</strong></a>, <a href="setup.html#index-13"><strong>[2]</strong></a>, <a href="setup.html#index-27"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controlport"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controlport&#39;]</strong></a>, <a href="config.html#index-64"><strong>[1]</strong></a>, <a href="setup.html#index-14"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controluser"><strong>$cfg[&#39;Servers&#39;][$i][&#39;controluser&#39;]</strong></a>, <a href="config.html#index-92"><strong>[1]</strong></a>, <a href="faq.html#index-16"><strong>[2]</strong></a>, <a href="setup.html#index-12"><strong>[3]</strong></a>, <a href="setup.html#index-26"><strong>[4]</strong></a>, <a href="setup.html#index-29"><strong>[5]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_designer_coords"><strong>$cfg[&#39;Servers&#39;][$i][&#39;designer_coords&#39;]</strong></a>, <a href="config.html#index-88"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_designer_settings"><strong>$cfg[&#39;Servers&#39;][$i][&#39;designer_settings&#39;]</strong></a>, <a href="config.html#index-116"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_DisableIS"><strong>$cfg[&#39;Servers&#39;][$i][&#39;DisableIS&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_export_templates"><strong>$cfg[&#39;Servers&#39;][$i][&#39;export_templates&#39;]</strong></a>, <a href="config.html#index-120"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_extension"><strong>$cfg[&#39;Servers&#39;][$i][&#39;extension&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_favorite"><strong>$cfg[&#39;Servers&#39;][$i][&#39;favorite&#39;]</strong></a>, <a href="config.html#index-103"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_hide_connection_errors"><strong>$cfg[&#39;Servers&#39;][$i][&#39;hide_connection_errors&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_hide_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;hide_db&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_history"><strong>$cfg[&#39;Servers&#39;][$i][&#39;history&#39;]</strong></a>, <a href="config.html#index-156"><strong>[1]</strong></a>, <a href="config.html#index-95"><strong>[2]</strong></a>, <a href="setup.html#index-8"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_host"><strong>$cfg[&#39;Servers&#39;][$i][&#39;host&#39;]</strong></a>, <a href="config.html#index-12"><strong>[1]</strong></a>, <a href="config.html#index-13"><strong>[2]</strong></a>, <a href="config.html#index-137"><strong>[3]</strong></a>, <a href="config.html#index-14"><strong>[4]</strong></a>, <a href="config.html#index-15"><strong>[5]</strong></a>, <a href="config.html#index-59"><strong>[6]</strong></a>, <a href="config.html#index-7"><strong>[7]</strong></a>, <a href="config.html#index-74"><strong>[8]</strong></a>, <a href="config.html#index-8"><strong>[9]</strong></a>, <a href="setup.html#index-1"><strong>[10]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_LogoutURL"><strong>$cfg[&#39;Servers&#39;][$i][&#39;LogoutURL&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>$cfg[&#39;Servers&#39;][$i][&#39;MaxTableUiprefs&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_navigationhiding"><strong>$cfg[&#39;Servers&#39;][$i][&#39;navigationhiding&#39;]</strong></a>, <a href="config.html#index-112"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_nopassword"><strong>$cfg[&#39;Servers&#39;][$i][&#39;nopassword&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_only_db"><strong>$cfg[&#39;Servers&#39;][$i][&#39;only_db&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_password"><strong>$cfg[&#39;Servers&#39;][$i][&#39;password&#39;]</strong></a>, <a href="setup.html#index-47"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_pdf_pages"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pdf_pages&#39;]</strong></a>, <a href="config.html#index-83"><strong>[1]</strong></a>, <a href="config.html#index-86"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_pmadb"><strong>$cfg[&#39;Servers&#39;][$i][&#39;pmadb&#39;]</strong></a>, <a href="config.html#index-1"><strong>[1]</strong></a>, <a href="config.html#index-102"><strong>[2]</strong></a>, <a href="config.html#index-105"><strong>[3]</strong></a>, <a href="config.html#index-108"><strong>[4]</strong></a>, <a href="config.html#index-111"><strong>[5]</strong></a>, <a href="config.html#index-113"><strong>[6]</strong></a>, <a href="config.html#index-115"><strong>[7]</strong></a>, <a href="config.html#index-117"><strong>[8]</strong></a>, <a href="config.html#index-119"><strong>[9]</strong></a>, <a href="config.html#index-121"><strong>[10]</strong></a>, <a href="config.html#index-123"><strong>[11]</strong></a>, <a href="config.html#index-124"><strong>[12]</strong></a>, <a href="config.html#index-143"><strong>[13]</strong></a>, <a href="config.html#index-62"><strong>[14]</strong></a>, <a href="config.html#index-76"><strong>[15]</strong></a>, <a href="config.html#index-77"><strong>[16]</strong></a>, <a href="config.html#index-79"><strong>[17]</strong></a>, <a href="config.html#index-81"><strong>[18]</strong></a>, <a href="config.html#index-84"><strong>[19]</strong></a>, <a href="config.html#index-89"><strong>[20]</strong></a>, <a href="config.html#index-94"><strong>[21]</strong></a>, <a href="config.html#index-98"><strong>[22]</strong></a>, <a href="setup.html#index-15"><strong>[23]</strong></a>, <a href="setup.html#index-7"><strong>[24]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_port"><strong>$cfg[&#39;Servers&#39;][$i][&#39;port&#39;]</strong></a>, <a href="config.html#index-11"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_recent"><strong>$cfg[&#39;Servers&#39;][$i][&#39;recent&#39;]</strong></a>, <a href="config.html#index-99"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_relation"><strong>$cfg[&#39;Servers&#39;][$i][&#39;relation&#39;]</strong></a>, <a href="config.html#index-80"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_savedsearches"><strong>$cfg[&#39;Servers&#39;][$i][&#39;savedsearches&#39;]</strong></a>, <a href="config.html#index-118"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SessionTimeZone"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SessionTimeZone&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonCookieParams"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonCookieParams&#39;]</strong></a>, <a href="setup.html#index-36"><strong>[1]</strong></a>, <a href="setup.html#index-42"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonScript"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonScript&#39;]</strong></a>, <a href="config.html#index-131"><strong>[1]</strong></a>, <a href="config.html#index-132"><strong>[2]</strong></a>, <a href="setup.html#index-37"><strong>[3]</strong></a>, <a href="setup.html#index-39"><strong>[4]</strong></a>, <a href="setup.html#index-43"><strong>[5]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonSession"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonSession&#39;]</strong></a>, <a href="setup.html#index-35"><strong>[1]</strong></a>, <a href="setup.html#index-41"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonURL"><strong>$cfg[&#39;Servers&#39;][$i][&#39;SignonURL&#39;]</strong></a>, <a href="setup.html#index-38"><strong>[1]</strong></a>, <a href="setup.html#index-44"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_socket"><strong>$cfg[&#39;Servers&#39;][$i][&#39;socket&#39;]</strong></a>, <a href="config.html#index-10"><strong>[1]</strong></a>, <a href="faq.html#index-7"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl&#39;]</strong></a>, <a href="config.html#index-161"><strong>[1]</strong></a>, <a href="config.html#index-166"><strong>[2]</strong></a>, <a href="config.html#index-22"><strong>[3]</strong></a>, <a href="config.html#index-28"><strong>[4]</strong></a>, <a href="config.html#index-34"><strong>[5]</strong></a>, <a href="config.html#index-40"><strong>[6]</strong></a>, <a href="config.html#index-46"><strong>[7]</strong></a>, <a href="config.html#index-52"><strong>[8]</strong></a>, <a href="config.html#index-66"><strong>[9]</strong></a>, <a href="setup.html#index-57"><strong>[10]</strong></a>, <a href="setup.html#index-64"><strong>[11]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_ca"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca&#39;]</strong></a>, <a href="config.html#index-164"><strong>[1]</strong></a>, <a href="config.html#index-167"><strong>[2]</strong></a>, <a href="config.html#index-18"><strong>[3]</strong></a>, <a href="config.html#index-24"><strong>[4]</strong></a>, <a href="config.html#index-30"><strong>[5]</strong></a>, <a href="config.html#index-43"><strong>[6]</strong></a>, <a href="config.html#index-49"><strong>[7]</strong></a>, <a href="config.html#index-55"><strong>[8]</strong></a>, <a href="config.html#index-69"><strong>[9]</strong></a>, <a href="setup.html#index-60"><strong>[10]</strong></a>, <a href="setup.html#index-67"><strong>[11]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_ca_path"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ca_path&#39;]</strong></a>, <a href="config.html#index-19"><strong>[1]</strong></a>, <a href="config.html#index-25"><strong>[2]</strong></a>, <a href="config.html#index-31"><strong>[3]</strong></a>, <a href="config.html#index-37"><strong>[4]</strong></a>, <a href="config.html#index-50"><strong>[5]</strong></a>, <a href="config.html#index-56"><strong>[6]</strong></a>, <a href="config.html#index-70"><strong>[7]</strong></a>, <a href="setup.html#index-61"><strong>[8]</strong></a>, <a href="setup.html#index-68"><strong>[9]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_cert"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_cert&#39;]</strong></a>, <a href="config.html#index-163"><strong>[1]</strong></a>, <a href="config.html#index-17"><strong>[2]</strong></a>, <a href="config.html#index-23"><strong>[3]</strong></a>, <a href="config.html#index-36"><strong>[4]</strong></a>, <a href="config.html#index-42"><strong>[5]</strong></a>, <a href="config.html#index-48"><strong>[6]</strong></a>, <a href="config.html#index-54"><strong>[7]</strong></a>, <a href="config.html#index-68"><strong>[8]</strong></a>, <a href="setup.html#index-59"><strong>[9]</strong></a>, <a href="setup.html#index-66"><strong>[10]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_ciphers"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_ciphers&#39;]</strong></a>, <a href="config.html#index-20"><strong>[1]</strong></a>, <a href="config.html#index-26"><strong>[2]</strong></a>, <a href="config.html#index-32"><strong>[3]</strong></a>, <a href="config.html#index-38"><strong>[4]</strong></a>, <a href="config.html#index-44"><strong>[5]</strong></a>, <a href="config.html#index-57"><strong>[6]</strong></a>, <a href="config.html#index-71"><strong>[7]</strong></a>, <a href="setup.html#index-69"><strong>[8]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_key"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_key&#39;]</strong></a>, <a href="config.html#index-16"><strong>[1]</strong></a>, <a href="config.html#index-162"><strong>[2]</strong></a>, <a href="config.html#index-29"><strong>[3]</strong></a>, <a href="config.html#index-35"><strong>[4]</strong></a>, <a href="config.html#index-41"><strong>[5]</strong></a>, <a href="config.html#index-47"><strong>[6]</strong></a>, <a href="config.html#index-53"><strong>[7]</strong></a>, <a href="config.html#index-67"><strong>[8]</strong></a>, <a href="setup.html#index-58"><strong>[9]</strong></a>, <a href="setup.html#index-65"><strong>[10]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_verify"><strong>$cfg[&#39;Servers&#39;][$i][&#39;ssl_verify&#39;]</strong></a>, <a href="config.html#index-165"><strong>[1]</strong></a>, <a href="config.html#index-168"><strong>[2]</strong></a>, <a href="config.html#index-21"><strong>[3]</strong></a>, <a href="config.html#index-27"><strong>[4]</strong></a>, <a href="config.html#index-33"><strong>[5]</strong></a>, <a href="config.html#index-39"><strong>[6]</strong></a>, <a href="config.html#index-45"><strong>[7]</strong></a>, <a href="config.html#index-51"><strong>[8]</strong></a>, <a href="config.html#index-58"><strong>[9]</strong></a>, <a href="config.html#index-72"><strong>[10]</strong></a>, <a href="setup.html#index-62"><strong>[11]</strong></a>, <a href="setup.html#index-70"><strong>[12]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_table_coords"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_coords&#39;]</strong></a>, <a href="config.html#index-85"><strong>[1]</strong></a>, <a href="config.html#index-87"><strong>[2]</strong></a>, <a href="relations.html#index-0"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_table_info"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_info&#39;]</strong></a>, <a href="config.html#index-82"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_table_uiprefs"><strong>$cfg[&#39;Servers&#39;][$i][&#39;table_uiprefs&#39;]</strong></a>, <a href="config.html#index-106"><strong>[1]</strong></a>, <a href="config.html#index-126"><strong>[2]</strong></a>, <a href="config.html#index-127"><strong>[3]</strong></a>, <a href="config.html#index-128"><strong>[4]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking&#39;]</strong></a>, <a href="config.html#index-122"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_database&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_table&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_add_drop_view&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_default_statements"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_default_statements&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>$cfg[&#39;Servers&#39;][$i][&#39;tracking_version_auto_create&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_user"><strong>$cfg[&#39;Servers&#39;][$i][&#39;user&#39;]</strong></a>, <a href="setup.html#index-46"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_userconfig"><strong>$cfg[&#39;Servers&#39;][$i][&#39;userconfig&#39;]</strong></a>, <a href="config.html#index-125"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_usergroups"><strong>$cfg[&#39;Servers&#39;][$i][&#39;usergroups&#39;]</strong></a>, <a href="config.html#index-107"><strong>[1]</strong></a>, <a href="config.html#index-110"><strong>[2]</strong></a>, <a href="privileges.html#index-1"><strong>[3]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_users"><strong>$cfg[&#39;Servers&#39;][$i][&#39;users&#39;]</strong></a>, <a href="config.html#index-109"><strong>[1]</strong></a>, <a href="privileges.html#index-0"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_verbose"><strong>$cfg[&#39;Servers&#39;][$i][&#39;verbose&#39;]</strong></a>, <a href="config.html#index-147"><strong>[1]</strong></a>, <a href="config.html#index-73"><strong>[2]</strong></a>, <a href="faq.html#index-23"><strong>[3]</strong></a>, <a href="setup.html#index-3"><strong>[4]</strong></a>
</li>
        <li><a href="config.html#cfg_SessionSavePath"><strong>$cfg[&#39;SessionSavePath&#39;]</strong></a>, <a href="setup.html#index-56"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowAll"><strong>$cfg[&#39;ShowAll&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowBrowseComments"><strong>$cfg[&#39;ShowBrowseComments&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowChgPassword"><strong>$cfg[&#39;ShowChgPassword&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowColumnComments"><strong>$cfg[&#39;ShowColumnComments&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowCreateDb"><strong>$cfg[&#39;ShowCreateDb&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowDatabasesNavigationAsTree"><strong>$cfg[&#39;ShowDatabasesNavigationAsTree&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowDbStructureCreation"><strong>$cfg[&#39;ShowDbStructureCreation&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowDbStructureLastCheck"><strong>$cfg[&#39;ShowDbStructureLastCheck&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowDbStructureLastUpdate"><strong>$cfg[&#39;ShowDbStructureLastUpdate&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowFieldTypesInDataEditView"><strong>$cfg[&#39;ShowFieldTypesInDataEditView&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowFunctionFields"><strong>$cfg[&#39;ShowFunctionFields&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowGitRevision"><strong>$cfg[&#39;ShowGitRevision&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowHint"><strong>$cfg[&#39;ShowHint&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowPhpInfo"><strong>$cfg[&#39;ShowPhpInfo&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowPropertyComments"><strong>$cfg[&#39;ShowPropertyComments&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowServerInfo"><strong>$cfg[&#39;ShowServerInfo&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowSQL"><strong>$cfg[&#39;ShowSQL&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ShowStats"><strong>$cfg[&#39;ShowStats&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SkipLockedTables"><strong>$cfg[&#39;SkipLockedTables&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SQLQuery_Edit"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Edit&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SQLQuery_Explain"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Explain&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SQLQuery_Refresh"><strong>$cfg[&#39;SQLQuery&#39;][&#39;Refresh&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SQLQuery_ShowAsPHP"><strong>$cfg[&#39;SQLQuery&#39;][&#39;ShowAsPHP&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_SuhosinDisableWarning"><strong>$cfg[&#39;SuhosinDisableWarning&#39;]</strong></a>, <a href="faq.html#index-5"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_TableNavigationLinksMode"><strong>$cfg[&#39;TableNavigationLinksMode&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TablePrimaryKeyOrder"><strong>$cfg[&#39;TablePrimaryKeyOrder&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TabsMode"><strong>$cfg[&#39;TabsMode&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TempDir"><strong>$cfg[&#39;TempDir&#39;]</strong></a>, <a href="config.html#index-3"><strong>[1]</strong></a>, <a href="faq.html#index-1"><strong>[2]</strong></a>, <a href="faq.html#index-25"><strong>[3]</strong></a>, <a href="setup.html#index-50"><strong>[4]</strong></a>
</li>
        <li><a href="config.html#cfg_TextareaAutoSelect"><strong>$cfg[&#39;TextareaAutoSelect&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TextareaCols"><strong>$cfg[&#39;TextareaCols&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TextareaRows"><strong>$cfg[&#39;TextareaRows&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ThemeDefault"><strong>$cfg[&#39;ThemeDefault&#39;]</strong></a>, <a href="themes.html#index-1"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_ThemeManager"><strong>$cfg[&#39;ThemeManager&#39;]</strong></a>, <a href="themes.html#index-0"><strong>[1]</strong></a>, <a href="themes.html#index-2"><strong>[2]</strong></a>
</li>
        <li><a href="config.html#cfg_ThemePerServer"><strong>$cfg[&#39;ThemePerServer&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TitleDatabase"><strong>$cfg[&#39;TitleDatabase&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TitleDefault"><strong>$cfg[&#39;TitleDefault&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TitleServer"><strong>$cfg[&#39;TitleServer&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TitleTable"><strong>$cfg[&#39;TitleTable&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TranslationWarningThreshold"><strong>$cfg[&#39;TranslationWarningThreshold&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_TrustedProxies"><strong>$cfg[&#39;TrustedProxies&#39;]</strong></a>, <a href="config.html#index-130"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_UploadDir"><strong>$cfg[&#39;UploadDir&#39;]</strong></a>, <a href="faq.html#index-2"><strong>[1]</strong></a>, <a href="faq.html#index-24"><strong>[2]</strong></a>, <a href="import_export.html#index-0"><strong>[3]</strong></a>, <a href="setup.html#index-18"><strong>[4]</strong></a>
</li>
        <li><a href="config.html#cfg_URLQueryEncryption"><strong>$cfg[&#39;URLQueryEncryption&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_URLQueryEncryptionSecretKey"><strong>$cfg[&#39;URLQueryEncryptionSecretKey&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_UseDbSearch"><strong>$cfg[&#39;UseDbSearch&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_UserprefsDeveloperTab"><strong>$cfg[&#39;UserprefsDeveloperTab&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_UserprefsDisallow"><strong>$cfg[&#39;UserprefsDisallow&#39;]</strong></a>, <a href="config.html#index-5"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_VersionCheck"><strong>$cfg[&#39;VersionCheck&#39;]</strong></a>
</li>
        <li><a href="config.html#cfg_ZeroConf"><strong>$cfg[&#39;ZeroConf&#39;]</strong></a>, <a href="setup.html#index-25"><strong>[1]</strong></a>
</li>
        <li><a href="config.html#cfg_ZipDump"><strong>$cfg[&#39;ZipDump&#39;]</strong></a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="setup.html#index-24">Configuration storage</a>
</li>
      <li><a href="config.html#cfg_Confirm"><strong>Confirm</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_connect_type"><strong>connect_type</strong></a>
</li>
      <li><a href="config.html#cfg_Console_AlwaysExpand"><strong>Console, AlwaysExpand</strong></a>
</li>
      <li><a href="config.html#cfg_Console_CurrentQuery"><strong>Console, CurrentQuery</strong></a>
</li>
      <li><a href="config.html#cfg_Console_DarkTheme"><strong>Console, DarkTheme</strong></a>
</li>
      <li><a href="config.html#cfg_Console_EnterExecutes"><strong>Console, EnterExecutes</strong></a>
</li>
      <li><a href="config.html#cfg_Console_Height"><strong>Console, Height</strong></a>
</li>
      <li><a href="config.html#cfg_Console_Mode"><strong>Console, Mode</strong></a>
</li>
      <li><a href="config.html#cfg_Console_StartHistory"><strong>Console, StartHistory</strong></a>
</li>
      <li><a href="config.html#cfg_ConsoleEnterExecutes"><strong>ConsoleEnterExecutes</strong></a>
</li>
      <li><a href="glossary.html#term-Content-Security-Policy"><strong>Content Security Policy</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_control_*"><strong>control_*</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controlhost"><strong>controlhost</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controlpass"><strong>controlpass</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controlport"><strong>controlport</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_controluser"><strong>controluser</strong></a>
</li>
      <li><a href="glossary.html#term-Cookie"><strong>Cookie</strong></a>

      <ul>
        <li><a href="setup.html#index-32">Authentication mode</a>
</li>
      </ul></li>
      <li><a href="config.html#cfg_CookieSameSite"><strong>CookieSameSite</strong></a>
</li>
      <li><a href="config.html#cfg_CSPAllow"><strong>CSPAllow</strong></a>
</li>
      <li><a href="glossary.html#term-CSV"><strong>CSV</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="import_export.html#data">data (global variable or constant)</a>
</li>
      <li><a href="glossary.html#term-Database"><strong>Database</strong></a>
</li>
      <li><a href="import_export.html#database">database (global variable or constant)</a>
</li>
      <li><a href="glossary.html#term-DB"><strong>DB</strong></a>
</li>
      <li><a href="config.html#cfg_DBG"><strong>DBG</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_demo"><strong>DBG, demo</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_simple2fa"><strong>DBG, simple2fa</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_sql"><strong>DBG, sql</strong></a>
</li>
      <li><a href="config.html#cfg_DBG_sqllog"><strong>DBG, sqllog</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultConnectionCollation"><strong>DefaultConnectionCollation</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultForeignKeyChecks"><strong>DefaultForeignKeyChecks</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultFunctions"><strong>DefaultFunctions</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultLang"><strong>DefaultLang</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultQueryDatabase"><strong>DefaultQueryDatabase</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultQueryTable"><strong>DefaultQueryTable</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTabDatabase"><strong>DefaultTabDatabase</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTabServer"><strong>DefaultTabServer</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_DefaultTabTable"><strong>DefaultTabTable</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations"><strong>DefaultTransformations</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Bool2Text"><strong>DefaultTransformations, Bool2Text</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_DateFormat"><strong>DefaultTransformations, DateFormat</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_External"><strong>DefaultTransformations, External</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Hex"><strong>DefaultTransformations, Hex</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Inline"><strong>DefaultTransformations, Inline</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_PreApPend"><strong>DefaultTransformations, PreApPend</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_Substring"><strong>DefaultTransformations, Substring</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_TextImageLink"><strong>DefaultTransformations, TextImageLink</strong></a>
</li>
      <li><a href="config.html#cfg_DefaultTransformations_TextLink"><strong>DefaultTransformations, TextLink</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_designer_coords"><strong>designer_coords</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_designer_settings"><strong>designer_settings</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_DisableIS"><strong>DisableIS</strong></a>
</li>
      <li><a href="config.html#cfg_DisableMultiTableMaintenance"><strong>DisableMultiTableMaintenance</strong></a>
</li>
      <li><a href="config.html#cfg_DisableShortcutKeys"><strong>DisableShortcutKeys</strong></a>
</li>
      <li><a href="config.html#cfg_DisplayServersList"><strong>DisplayServersList</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_enable_drag_drop_import"><strong>enable_drag_drop_import</strong></a>
</li>
      <li><a href="config.html#cfg_EnableAutocompleteForTablesAndColumns"><strong>EnableAutocompleteForTablesAndColumns</strong></a>
</li>
      <li><a href="glossary.html#term-Engine"><strong>Engine</strong></a>
</li>
      <li><a href="config.html#cfg_environment"><strong>environment</strong></a>
</li>
      <li>
    environment variable

      <ul>
        <li><a href="setup.html#envvar-APACHE_PORT">APACHE_PORT</a>
</li>
        <li><a href="setup.html#envvar-HIDE_PHP_VERSION">HIDE_PHP_VERSION</a>
</li>
        <li><a href="setup.html#envvar-MAX_EXECUTION_TIME">MAX_EXECUTION_TIME</a>
</li>
        <li><a href="setup.html#envvar-MEMORY_LIMIT">MEMORY_LIMIT</a>
</li>
        <li><a href="setup.html#envvar-PMA_ABSOLUTE_URI">PMA_ABSOLUTE_URI</a>, <a href="setup.html#index-22">[1]</a>
</li>
        <li><a href="setup.html#envvar-PMA_ARBITRARY">PMA_ARBITRARY</a>
</li>
        <li><a href="setup.html#envvar-PMA_CONFIG_BASE64">PMA_CONFIG_BASE64</a>
</li>
        <li><a href="setup.html#envvar-PMA_CONTROLHOST">PMA_CONTROLHOST</a>
</li>
        <li><a href="setup.html#envvar-PMA_CONTROLPASS">PMA_CONTROLPASS</a>
</li>
        <li><a href="setup.html#envvar-PMA_CONTROLPORT">PMA_CONTROLPORT</a>
</li>
        <li><a href="setup.html#envvar-PMA_CONTROLUSER">PMA_CONTROLUSER</a>
</li>
        <li><a href="setup.html#envvar-PMA_HOST">PMA_HOST</a>, <a href="setup.html#index-2">[1]</a>
</li>
        <li><a href="setup.html#envvar-PMA_HOSTS">PMA_HOSTS</a>
</li>
        <li><a href="setup.html#envvar-PMA_PASSWORD">PMA_PASSWORD</a>, <a href="setup.html#index-21">[1]</a>
</li>
        <li><a href="setup.html#envvar-PMA_PMADB">PMA_PMADB</a>
</li>
        <li><a href="setup.html#envvar-PMA_PORT">PMA_PORT</a>, <a href="setup.html#index-5">[1]</a>
</li>
        <li><a href="setup.html#envvar-PMA_PORTS">PMA_PORTS</a>
</li>
        <li><a href="setup.html#envvar-PMA_QUERYHISTORYDB">PMA_QUERYHISTORYDB</a>
</li>
        <li><a href="setup.html#envvar-PMA_QUERYHISTORYMAX">PMA_QUERYHISTORYMAX</a>
</li>
        <li><a href="setup.html#envvar-PMA_SAVEDIR">PMA_SAVEDIR</a>
</li>
        <li><a href="setup.html#envvar-PMA_UPLOADDIR">PMA_UPLOADDIR</a>
</li>
        <li><a href="setup.html#envvar-PMA_USER">PMA_USER</a>, <a href="setup.html#index-20">[1]</a>
</li>
        <li><a href="setup.html#envvar-PMA_USER_CONFIG_BASE64">PMA_USER_CONFIG_BASE64</a>
</li>
        <li><a href="setup.html#envvar-PMA_VERBOSE">PMA_VERBOSE</a>, <a href="setup.html#index-4">[1]</a>
</li>
        <li><a href="setup.html#envvar-PMA_VERBOSES">PMA_VERBOSES</a>
</li>
        <li><a href="setup.html#envvar-UPLOAD_LIMIT">UPLOAD_LIMIT</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_ExecTimeLimit"><strong>ExecTimeLimit</strong></a>
</li>
      <li><a href="config.html#cfg_Export"><strong>Export</strong></a>
</li>
      <li><a href="config.html#cfg_Export_charset"><strong>Export, charset</strong></a>
</li>
      <li><a href="config.html#cfg_Export_file_template_database"><strong>Export, file_template_database</strong></a>
</li>
      <li><a href="config.html#cfg_Export_file_template_server"><strong>Export, file_template_server</strong></a>
</li>
      <li><a href="config.html#cfg_Export_file_template_table"><strong>Export, file_template_table</strong></a>
</li>
      <li><a href="config.html#cfg_Export_format"><strong>Export, format</strong></a>
</li>
      <li><a href="config.html#cfg_Export_method"><strong>Export, method</strong></a>
</li>
      <li><a href="config.html#cfg_Export_remove_definer_from_definitions"><strong>Export, remove_definer_from_definitions</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_export_templates"><strong>export_templates</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_extension"><strong>extension</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-FAQ"><strong>FAQ</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_favorite"><strong>favorite</strong></a>
</li>
      <li><a href="glossary.html#term-Field"><strong>Field</strong></a>
</li>
      <li><a href="config.html#cfg_FilterLanguages"><strong>FilterLanguages</strong></a>
</li>
      <li><a href="config.html#cfg_FirstDayOfCalendar"><strong>FirstDayOfCalendar</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_FirstLevelNavigationItems"><strong>FirstLevelNavigationItems</strong></a>
</li>
      <li><a href="config.html#cfg_FontSize"><strong>FontSize</strong></a>
</li>
      <li><a href="config.html#cfg_ForceSSL"><strong>ForceSSL</strong></a>
</li>
      <li><a href="glossary.html#term-Foreign-key"><strong>Foreign key</strong></a>
</li>
      <li><a href="config.html#cfg_ForeignKeyDropdownOrder"><strong>ForeignKeyDropdownOrder</strong></a>
</li>
      <li><a href="config.html#cfg_ForeignKeyMaxLimit"><strong>ForeignKeyMaxLimit</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-GD"><strong>GD</strong></a>
</li>
      <li><a href="glossary.html#term-GD2"><strong>GD2</strong></a>
</li>
      <li><a href="config.html#cfg_GD2Available"><strong>GD2Available</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_GridEditing"><strong>GridEditing</strong></a>
</li>
      <li><a href="glossary.html#term-GZip"><strong>GZip</strong></a>
</li>
      <li><a href="config.html#cfg_GZipDump"><strong>GZipDump</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Servers_hide_connection_errors"><strong>hide_connection_errors</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_hide_db"><strong>hide_db</strong></a>
</li>
      <li><a href="config.html#cfg_HideStructureActions"><strong>HideStructureActions</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_history"><strong>history</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Servers_host"><strong>host</strong></a>, <a href="glossary.html#term-host"><strong>[1]</strong></a>
</li>
      <li><a href="glossary.html#term-hostname"><strong>hostname</strong></a>
</li>
      <li><a href="glossary.html#term-HTTP"><strong>HTTP</strong></a>

      <ul>
        <li><a href="setup.html#index-31">Authentication mode</a>
</li>
      </ul></li>
      <li><a href="glossary.html#term-HTTPS"><strong>HTTPS</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_IconvExtraParams"><strong>IconvExtraParams</strong></a>
</li>
      <li><a href="glossary.html#term-IEC"><strong>IEC</strong></a>
</li>
      <li><a href="config.html#cfg_IgnoreMultiSubmitErrors"><strong>IgnoreMultiSubmitErrors</strong></a>
</li>
      <li><a href="glossary.html#term-IIS"><strong>IIS</strong></a>
</li>
      <li><a href="config.html#cfg_Import"><strong>Import</strong></a>
</li>
      <li><a href="config.html#cfg_Import_charset"><strong>Import, charset</strong></a>
</li>
      <li><a href="glossary.html#term-Index"><strong>Index</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_InitialSlidersState"><strong>InitialSlidersState</strong></a>
</li>
      <li><a href="config.html#cfg_InsertRows"><strong>InsertRows</strong></a>
</li>
      <li><a href="glossary.html#term-IP"><strong>IP</strong></a>
</li>
      <li><a href="glossary.html#term-IP-Address"><strong>IP Address</strong></a>
</li>
      <li><a href="glossary.html#term-IPv6"><strong>IPv6</strong></a>
</li>
      <li><a href="glossary.html#term-ISAPI"><strong>ISAPI</strong></a>
</li>
      <li><a href="glossary.html#term-ISO"><strong>ISO</strong></a>
</li>
      <li><a href="glossary.html#term-ISP"><strong>ISP</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="J">J</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-JPEG"><strong>JPEG</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-JPG"><strong>JPG</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="K">K</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Key"><strong>Key</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Lang"><strong>Lang</strong></a>
</li>
      <li><a href="glossary.html#term-LATEX"><strong>LATEX</strong></a>
</li>
      <li><a href="config.html#cfg_LimitChars"><strong>LimitChars</strong></a>
</li>
      <li><a href="config.html#cfg_LinkLengthLimit"><strong>LinkLengthLimit</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieDeleteAll"><strong>LoginCookieDeleteAll</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_LoginCookieRecall"><strong>LoginCookieRecall</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieStore"><strong>LoginCookieStore</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieValidity"><strong>LoginCookieValidity</strong></a>
</li>
      <li><a href="config.html#cfg_LoginCookieValidityDisableWarning"><strong>LoginCookieValidityDisableWarning</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_LogoutURL"><strong>LogoutURL</strong></a>
</li>
      <li><a href="config.html#cfg_LongtextDoubleTextarea"><strong>LongtextDoubleTextarea</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Mac"><strong>Mac</strong></a>
</li>
      <li><a href="glossary.html#term-macOS"><strong>macOS</strong></a>
</li>
      <li><a href="config.html#cfg_MaxCharactersInDisplayedSQL"><strong>MaxCharactersInDisplayedSQL</strong></a>
</li>
      <li><a href="config.html#cfg_MaxDbList"><strong>MaxDbList</strong></a>
</li>
      <li><a href="config.html#cfg_MaxExactCount"><strong>MaxExactCount</strong></a>
</li>
      <li><a href="config.html#cfg_MaxExactCountViews"><strong>MaxExactCountViews</strong></a>
</li>
      <li><a href="config.html#cfg_MaxNavigationItems"><strong>MaxNavigationItems</strong></a>
</li>
      <li><a href="config.html#cfg_MaxRows"><strong>MaxRows</strong></a>
</li>
      <li><a href="config.html#cfg_MaxSizeForInputField"><strong>MaxSizeForInputField</strong></a>
</li>
      <li><a href="config.html#cfg_MaxTableList"><strong>MaxTableList</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>MaxTableUiprefs</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-mbstring"><strong>mbstring</strong></a>
</li>
      <li><a href="glossary.html#term-Media-type"><strong>Media type</strong></a>
</li>
      <li><a href="config.html#cfg_MemoryLimit"><strong>MemoryLimit</strong></a>
</li>
      <li><a href="glossary.html#term-MIME"><strong>MIME</strong></a>
</li>
      <li><a href="config.html#cfg_MinSizeForInputField"><strong>MinSizeForInputField</strong></a>
</li>
      <li><a href="glossary.html#term-mod_proxy_fcgi"><strong>mod_proxy_fcgi</strong></a>
</li>
      <li><a href="glossary.html#term-module"><strong>module</strong></a>
</li>
      <li><a href="glossary.html#term-MySQL"><strong>MySQL</strong></a>
</li>
      <li><a href="glossary.html#term-mysql"><strong>mysql</strong></a>
</li>
      <li><a href="glossary.html#term-MySQLi"><strong>MySQLi</strong></a>
</li>
      <li><a href="config.html#cfg_MysqlMinVersion"><strong>MysqlMinVersion</strong></a>
</li>
      <li><a href="config.html#cfg_MysqlSslWarningSafeHosts"><strong>MysqlSslWarningSafeHosts</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="N">N</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="import_export.html#name">name (global variable or constant)</a>
</li>
      <li><a href="config.html#cfg_NaturalOrder"><strong>NaturalOrder</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationDisplayLogo"><strong>NavigationDisplayLogo</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationDisplayServers"><strong>NavigationDisplayServers</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_navigationhiding"><strong>navigationhiding</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationLinkWithMainPanel"><strong>NavigationLinkWithMainPanel</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationLogoLink"><strong>NavigationLogoLink</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationLogoLinkWindow"><strong>NavigationLogoLinkWindow</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDbSeparator"><strong>NavigationTreeDbSeparator</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDefaultTabTable"><strong>NavigationTreeDefaultTabTable</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDefaultTabTable2"><strong>NavigationTreeDefaultTabTable2</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDisplayDbFilterMinimum"><strong>NavigationTreeDisplayDbFilterMinimum</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeDisplayItemFilterMinimum"><strong>NavigationTreeDisplayItemFilterMinimum</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_NavigationTreeEnableExpansion"><strong>NavigationTreeEnableExpansion</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeEnableGrouping"><strong>NavigationTreeEnableGrouping</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreePointerEnable"><strong>NavigationTreePointerEnable</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowEvents"><strong>NavigationTreeShowEvents</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowFunctions"><strong>NavigationTreeShowFunctions</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowProcedures"><strong>NavigationTreeShowProcedures</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowTables"><strong>NavigationTreeShowTables</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeShowViews"><strong>NavigationTreeShowViews</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeTableLevel"><strong>NavigationTreeTableLevel</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationTreeTableSeparator"><strong>NavigationTreeTableSeparator</strong></a>
</li>
      <li><a href="config.html#cfg_NavigationWidth"><strong>NavigationWidth</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_nopassword"><strong>nopassword</strong></a>
</li>
      <li><a href="config.html#cfg_NumFavoriteTables"><strong>NumFavoriteTables</strong></a>
</li>
      <li><a href="config.html#cfg_NumRecentTables"><strong>NumRecentTables</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_OBGzip"><strong>OBGzip</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_only_db"><strong>only_db</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-OpenDocument"><strong>OpenDocument</strong></a>
</li>
      <li><a href="config.html#cfg_Order"><strong>Order</strong></a>
</li>
      <li><a href="glossary.html#term-OS-X"><strong>OS X</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Servers_password"><strong>password</strong></a>
</li>
      <li><a href="glossary.html#term-PCRE"><strong>PCRE</strong></a>
</li>
      <li><a href="glossary.html#term-PDF"><strong>PDF</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_pdf_pages"><strong>pdf_pages</strong></a>
</li>
      <li><a href="config.html#cfg_PDFDefaultPageSize"><strong>PDFDefaultPageSize</strong></a>
</li>
      <li><a href="config.html#cfg_PDFPageSizes"><strong>PDFPageSizes</strong></a>
</li>
      <li><a href="glossary.html#term-PEAR"><strong>PEAR</strong></a>
</li>
      <li><a href="config.html#cfg_PersistentConnections"><strong>PersistentConnections</strong></a>
</li>
      <li><a href="glossary.html#term-PHP"><strong>PHP</strong></a>
</li>
      <li><a href="glossary.html#term-PHP-extension"><strong>PHP extension</strong></a>
</li>
      <li><a href="setup.html#index-24">phpMyAdmin configuration storage</a>
</li>
      <li><a href="setup.html#index-22">PMA_ABSOLUTE_URI</a>
</li>
      <li><a href="setup.html#index-2">PMA_HOST</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="setup.html#index-21">PMA_PASSWORD</a>
</li>
      <li><a href="setup.html#index-5">PMA_PORT</a>
</li>
      <li><a href="setup.html#index-20">PMA_USER</a>
</li>
      <li><a href="setup.html#index-4">PMA_VERBOSE</a>
</li>
      <li><a href="config.html#cfg_PmaAbsoluteUri"><strong>PmaAbsoluteUri</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_pmadb"><strong>pmadb</strong></a>, <a href="setup.html#index-24">[1]</a>
</li>
      <li><a href="config.html#cfg_PmaNoRelation_DisableWarning"><strong>PmaNoRelation_DisableWarning</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_port"><strong>port</strong></a>, <a href="glossary.html#term-port"><strong>[1]</strong></a>
</li>
      <li><a href="glossary.html#term-primary-key"><strong>primary key</strong></a>
</li>
      <li><a href="config.html#cfg_PropertiesNumColumns"><strong>PropertiesNumColumns</strong></a>
</li>
      <li><a href="config.html#cfg_ProtectBinary"><strong>ProtectBinary</strong></a>
</li>
      <li><a href="config.html#cfg_ProxyPass"><strong>ProxyPass</strong></a>
</li>
      <li><a href="config.html#cfg_ProxyUrl"><strong>ProxyUrl</strong></a>
</li>
      <li><a href="config.html#cfg_ProxyUser"><strong>ProxyUser</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_QueryHistoryDB"><strong>QueryHistoryDB</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_QueryHistoryMax"><strong>QueryHistoryMax</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Servers_recent"><strong>recent</strong></a>
</li>
      <li><a href="config.html#cfg_RecodingEngine"><strong>RecodingEngine</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_relation"><strong>relation</strong></a>
</li>
      <li><a href="config.html#cfg_RelationalDisplay"><strong>RelationalDisplay</strong></a>
</li>
      <li><a href="config.html#cfg_RememberSorting"><strong>RememberSorting</strong></a>
</li>
      <li><a href="config.html#cfg_RepeatCells"><strong>RepeatCells</strong></a>
</li>
      <li><a href="config.html#cfg_ReservedWordDisableWarning"><strong>ReservedWordDisableWarning</strong></a>
</li>
      <li><a href="config.html#cfg_RetainQueryBox"><strong>RetainQueryBox</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-RFC"><strong>RFC</strong></a>

      <ul>
        <li><a href="glossary.html#index-0">RFC 1952</a>
</li>
        <li><a href="faq.html#index-4">RFC 2616</a>
</li>
      </ul></li>
      <li><a href="glossary.html#term-RFC-1952"><strong>RFC 1952</strong></a>
</li>
      <li><a href="glossary.html#term-Row-record-tuple"><strong>Row (record, tuple)</strong></a>
</li>
      <li><a href="config.html#cfg_RowActionLinks"><strong>RowActionLinks</strong></a>
</li>
      <li><a href="config.html#cfg_RowActionLinksWithoutUnique"><strong>RowActionLinksWithoutUnique</strong></a>
</li>
      <li><a href="config.html#cfg_RowActionType"><strong>RowActionType</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_SaveCellsAtOnce"><strong>SaveCellsAtOnce</strong></a>
</li>
      <li><a href="config.html#cfg_SaveDir"><strong>SaveDir</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_savedsearches"><strong>savedsearches</strong></a>
</li>
      <li><a href="config.html#cfg_SendErrorReports"><strong>SendErrorReports</strong></a>
</li>
      <li><a href="glossary.html#term-Server"><strong>Server</strong></a>
</li>
      <li>
    server configuration

      <ul>
        <li><a href="config.html#cfg_Servers_AllowDeny_order"><strong>AllowDeny, order</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_AllowDeny_rules"><strong>AllowDeny, rules</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_AllowNoPassword"><strong>AllowNoPassword</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_AllowRoot"><strong>AllowRoot</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_auth_http_realm"><strong>auth_http_realm</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_auth_swekey_config"><strong>auth_swekey_config</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_auth_type"><strong>auth_type</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_bookmarktable"><strong>bookmarktable</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_central_columns"><strong>central_columns</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_column_info"><strong>column_info</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_compress"><strong>compress</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_connect_type"><strong>connect_type</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_control_*"><strong>control_*</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controlhost"><strong>controlhost</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controlpass"><strong>controlpass</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controlport"><strong>controlport</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_controluser"><strong>controluser</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_designer_coords"><strong>designer_coords</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_designer_settings"><strong>designer_settings</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_DisableIS"><strong>DisableIS</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_export_templates"><strong>export_templates</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_extension"><strong>extension</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_favorite"><strong>favorite</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_hide_connection_errors"><strong>hide_connection_errors</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_hide_db"><strong>hide_db</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_history"><strong>history</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_host"><strong>host</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_LogoutURL"><strong>LogoutURL</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_MaxTableUiprefs"><strong>MaxTableUiprefs</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_navigationhiding"><strong>navigationhiding</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_nopassword"><strong>nopassword</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_only_db"><strong>only_db</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_password"><strong>password</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_pdf_pages"><strong>pdf_pages</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_pmadb"><strong>pmadb</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_port"><strong>port</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_recent"><strong>recent</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_relation"><strong>relation</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_savedsearches"><strong>savedsearches</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SessionTimeZone"><strong>SessionTimeZone</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonCookieParams"><strong>SignonCookieParams</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonScript"><strong>SignonScript</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonSession"><strong>SignonSession</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_SignonURL"><strong>SignonURL</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_socket"><strong>socket</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl"><strong>ssl</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_ca"><strong>ssl_ca</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_ca_path"><strong>ssl_ca_path</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_cert"><strong>ssl_cert</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_ciphers"><strong>ssl_ciphers</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_key"><strong>ssl_key</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_ssl_verify"><strong>ssl_verify</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_table_coords"><strong>table_coords</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_table_info"><strong>table_info</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_table_uiprefs"><strong>table_uiprefs</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking"><strong>tracking</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>tracking_add_drop_database</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>tracking_add_drop_table</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>tracking_add_drop_view</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_default_statements"><strong>tracking_default_statements</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>tracking_version_auto_create</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_user"><strong>user</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_userconfig"><strong>userconfig</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_usergroups"><strong>usergroups</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_users"><strong>users</strong></a>
</li>
        <li><a href="config.html#cfg_Servers_verbose"><strong>verbose</strong></a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_ServerDefault"><strong>ServerDefault</strong></a>
</li>
      <li><a href="config.html#cfg_ServerLibraryDifference_DisableWarning"><strong>ServerLibraryDifference_DisableWarning</strong></a>
</li>
      <li><a href="config.html#cfg_Servers"><strong>Servers</strong></a>
</li>
      <li><a href="config.html#cfg_SessionSavePath"><strong>SessionSavePath</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SessionTimeZone"><strong>SessionTimeZone</strong></a>
</li>
      <li><a href="setup.html#index-23">Setup script</a>
</li>
      <li><a href="config.html#cfg_ShowAll"><strong>ShowAll</strong></a>
</li>
      <li><a href="config.html#cfg_ShowBrowseComments"><strong>ShowBrowseComments</strong></a>
</li>
      <li><a href="config.html#cfg_ShowChgPassword"><strong>ShowChgPassword</strong></a>
</li>
      <li><a href="config.html#cfg_ShowColumnComments"><strong>ShowColumnComments</strong></a>
</li>
      <li><a href="config.html#cfg_ShowCreateDb"><strong>ShowCreateDb</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDatabasesNavigationAsTree"><strong>ShowDatabasesNavigationAsTree</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDbStructureCreation"><strong>ShowDbStructureCreation</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDbStructureLastCheck"><strong>ShowDbStructureLastCheck</strong></a>
</li>
      <li><a href="config.html#cfg_ShowDbStructureLastUpdate"><strong>ShowDbStructureLastUpdate</strong></a>
</li>
      <li><a href="config.html#cfg_ShowFieldTypesInDataEditView"><strong>ShowFieldTypesInDataEditView</strong></a>
</li>
      <li><a href="config.html#cfg_ShowFunctionFields"><strong>ShowFunctionFields</strong></a>
</li>
      <li><a href="config.html#cfg_ShowGitRevision"><strong>ShowGitRevision</strong></a>
</li>
      <li><a href="config.html#cfg_ShowHint"><strong>ShowHint</strong></a>
</li>
      <li><a href="config.html#cfg_ShowPhpInfo"><strong>ShowPhpInfo</strong></a>
</li>
      <li><a href="config.html#cfg_ShowPropertyComments"><strong>ShowPropertyComments</strong></a>
</li>
      <li><a href="config.html#cfg_ShowServerInfo"><strong>ShowServerInfo</strong></a>
</li>
      <li><a href="config.html#cfg_ShowSQL"><strong>ShowSQL</strong></a>
</li>
      <li><a href="config.html#cfg_ShowStats"><strong>ShowStats</strong></a>
</li>
      <li>
    Signon

      <ul>
        <li><a href="setup.html#index-34">Authentication mode</a>
</li>
      </ul></li>
      <li><a href="config.html#cfg_Servers_SignonCookieParams"><strong>SignonCookieParams</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SignonScript"><strong>SignonScript</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SignonSession"><strong>SignonSession</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_SignonURL"><strong>SignonURL</strong></a>
</li>
      <li><a href="config.html#cfg_SkipLockedTables"><strong>SkipLockedTables</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_socket"><strong>socket</strong></a>, <a href="glossary.html#term-socket"><strong>[1]</strong></a>
</li>
      <li><a href="glossary.html#term-Sodium"><strong>Sodium</strong></a>
</li>
      <li><a href="glossary.html#term-SQL"><strong>SQL</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_Edit"><strong>SQLQuery, Edit</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_Explain"><strong>SQLQuery, Explain</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_Refresh"><strong>SQLQuery, Refresh</strong></a>
</li>
      <li><a href="config.html#cfg_SQLQuery_ShowAsPHP"><strong>SQLQuery, ShowAsPHP</strong></a>
</li>
      <li><a href="glossary.html#term-SSL"><strong>SSL</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl"><strong>ssl</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_ca"><strong>ssl_ca</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_ca_path"><strong>ssl_ca_path</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_cert"><strong>ssl_cert</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_ciphers"><strong>ssl_ciphers</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_key"><strong>ssl_key</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_ssl_verify"><strong>ssl_verify</strong></a>
</li>
      <li><a href="glossary.html#term-Storage-Engines"><strong>Storage Engines</strong></a>
</li>
      <li><a href="glossary.html#term-Stored-procedure"><strong>Stored procedure</strong></a>
</li>
      <li><a href="config.html#cfg_SuhosinDisableWarning"><strong>SuhosinDisableWarning</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-table"><strong>table</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_table_coords"><strong>table_coords</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_table_info"><strong>table_info</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_table_uiprefs"><strong>table_uiprefs</strong></a>
</li>
      <li><a href="config.html#cfg_TableNavigationLinksMode"><strong>TableNavigationLinksMode</strong></a>
</li>
      <li><a href="config.html#cfg_TablePrimaryKeyOrder"><strong>TablePrimaryKeyOrder</strong></a>
</li>
      <li><a href="config.html#cfg_TabsMode"><strong>TabsMode</strong></a>
</li>
      <li><a href="glossary.html#term-tar"><strong>tar</strong></a>
</li>
      <li><a href="glossary.html#term-TCP"><strong>TCP</strong></a>
</li>
      <li><a href="glossary.html#term-TCPDF"><strong>TCPDF</strong></a>
</li>
      <li><a href="config.html#cfg_TempDir"><strong>TempDir</strong></a>
</li>
      <li><a href="config.html#cfg_TextareaAutoSelect"><strong>TextareaAutoSelect</strong></a>
</li>
      <li><a href="config.html#cfg_TextareaCols"><strong>TextareaCols</strong></a>
</li>
      <li><a href="config.html#cfg_TextareaRows"><strong>TextareaRows</strong></a>
</li>
      <li><a href="config.html#cfg_ThemeDefault"><strong>ThemeDefault</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_ThemeManager"><strong>ThemeManager</strong></a>
</li>
      <li><a href="config.html#cfg_ThemePerServer"><strong>ThemePerServer</strong></a>
</li>
      <li><a href="config.html#cfg_TitleDatabase"><strong>TitleDatabase</strong></a>
</li>
      <li><a href="config.html#cfg_TitleDefault"><strong>TitleDefault</strong></a>
</li>
      <li><a href="config.html#cfg_TitleServer"><strong>TitleServer</strong></a>
</li>
      <li><a href="config.html#cfg_TitleTable"><strong>TitleTable</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking"><strong>tracking</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_add_drop_database"><strong>tracking_add_drop_database</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_add_drop_table"><strong>tracking_add_drop_table</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_add_drop_view"><strong>tracking_add_drop_view</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_default_statements"><strong>tracking_default_statements</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_tracking_version_auto_create"><strong>tracking_version_auto_create</strong></a>
</li>
      <li><a href="config.html#cfg_TranslationWarningThreshold"><strong>TranslationWarningThreshold</strong></a>
</li>
      <li><a href="glossary.html#term-trigger"><strong>trigger</strong></a>
</li>
      <li><a href="config.html#cfg_TrustedProxies"><strong>TrustedProxies</strong></a>
</li>
      <li><a href="import_export.html#type">type (global variable or constant)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-unique-key"><strong>unique key</strong></a>
</li>
      <li><a href="config.html#cfg_UploadDir"><strong>UploadDir</strong></a>
</li>
      <li><a href="glossary.html#term-URL"><strong>URL</strong></a>
</li>
      <li><a href="config.html#cfg_URLQueryEncryption"><strong>URLQueryEncryption</strong></a>
</li>
      <li><a href="config.html#cfg_URLQueryEncryptionSecretKey"><strong>URLQueryEncryptionSecretKey</strong></a>
</li>
      <li><a href="config.html#cfg_UseDbSearch"><strong>UseDbSearch</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Servers_user"><strong>user</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_userconfig"><strong>userconfig</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_usergroups"><strong>usergroups</strong></a>
</li>
      <li><a href="config.html#cfg_UserprefsDeveloperTab"><strong>UserprefsDeveloperTab</strong></a>
</li>
      <li><a href="config.html#cfg_UserprefsDisallow"><strong>UserprefsDisallow</strong></a>
</li>
      <li><a href="config.html#cfg_Servers_users"><strong>users</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_Servers_verbose"><strong>verbose</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="import_export.html#version">version (global variable or constant)</a>
</li>
      <li><a href="config.html#cfg_VersionCheck"><strong>VersionCheck</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="W">W</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Web-server"><strong>Web server</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="X">X</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-XML"><strong>XML</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="Z">Z</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_ZeroConf"><strong>ZeroConf</strong></a>
</li>
      <li><a href="glossary.html#term-ZIP"><strong>ZIP</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="config.html#cfg_ZipDump"><strong>ZipDump</strong></a>
</li>
      <li><a href="glossary.html#term-Zlib"><strong>Zlib</strong></a>
</li>
  </ul></td>
</tr></table>



            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="#" title="General Index"
             >index</a></li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Index</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>