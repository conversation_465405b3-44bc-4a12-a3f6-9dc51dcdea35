@import url('https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
*{
    margin: 0;
    padding:0;
    box-sizing: border-box;
    font-family:'Prompt', sans-serif;
}
body{
    background: linear-gradient(120deg, #2980b9, #8e44ad);
    margin: 0;
    line-height: 1.5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
}

.container{
    display: flex;
    flex-direction: column;
    background: #fff;
    width: 60%;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 0px 2.2px rgba(0, 0, 0, 0.028), 0 0px 5.3px rgba(0, 0, 0, 0.04), 0 0px 10px rgba(0, 0, 0, 0.05);
}

h2{
    margin-top: 2vw;
    text-align: center;
    font-size: 2.1vw;
    padding: 5px;

}

form {
    margin: 2vw 0 5vw 0;
 
}

.form-grop button{
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%);
    width: 40%;
    background: red;
    border: none;
    outline: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1.2em;
    color: #e9f4fb;
    font-weight: 700;
    margin-top: 30px;
    margin-bottom: 20px;
    padding: 20px;
}

.form-grop a{
    display: block;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%);
    width: 80%;
    padding: 3px 3px 3px 5px;
    outline: none;
    text-align: center;
    text-decoration: none;
    background: #2691d9;
    font-size: 1.2em;
    font-weight: 700;
    color: #e9f4fb;
    padding: 20px;
    margin-top: 30px;
    margin-bottom: 40px;
    border-radius: 10px;
}
