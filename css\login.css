@import url("https://fonts.googleapis.com/css?family=Sofia");

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'poppins', sans-serif;
    background: linear-gradient(120deg, #2980b9, #8e44ad);
    height: 100vh;
    overflow: hidden;
}

.center {
    position: absolute;
    top:50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    background: #fff;
    border-radius: 10px;
}

.center h1 {
    text-align: center;
    padding: 40px 0 20px 0;
    border-bottom: 1px solid silver;
}

.center form { 
    padding: 0 40px;
    margin: 20px 0;
    box-sizing: border-box;
}

form .txt_field {
    position: relative;
    border-bottom: 2px solid #2691d9;
    margin: 30px 0;
}

.txt_field input {
    width: 100%;
    padding: 0 5px;
    height: 40px;
    font-size: 16px;
    border: none;
    background: none;
    outline: none;
}

.txt_field input:valid ~ .icon i{
    display:inline;
}

.txt_field .icon i{
    position: absolute;
    right: 5px;
    color: #adadad;
    font-size: 20px;
    line-height: 45px;
    cursor: pointer;
    background: #fff;
    padding-left:10px;
    z-index: 1;
    display: none;
}

/* .txt_field .icon{
    position: absolute;
    right: 8px;
    color: #adadad;
    font-size: 20px;
    line-height: 45px;
    cursor: pointer;
    background: #fff;
    padding-left:10px;
    z-index: 1;
    display: none;
}

.txt_field .icon .hide:before {
    content: "\f095";
    color: #000;
} */

.txt_field label {
    position: absolute;
    top: 50%;
    left: 5px;
    color: #adadad;
    transform: translateY(-50%);
    font-size: 16px;
    pointer-events: none;
    transition: .5s;
}

.txt_field span::before {
    content: '';
    position: absolute;
    top: 40px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #2691d9;
    transition: .5s;
    z-index: 2;
}

.txt_field input:focus ~ label,
.txt_field input:valid ~ label {
    top: -5px;
    color: #2691d9;
}
.txt_field input:focus ~ span::before,
.txt_field input:valid ~ span::before {
    width: 100%;
}

input[type="submit"] {
    width: 100%;
    height: 50px;
    border: 1px solid;
    background: #2691d9;
    border-radius: 25px;
    font-size: 18px;
    color: #e9f4fb;
    font-weight: 700;
    cursor: pointer;
    outline: none;
    margin: 20px 0 30px 0;
}

input[type="submit"]:hover {
    border-color: #2691d9;
    transition: .5s;
}