
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bookmarks &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="User management" href="privileges.html" />
    <link rel="prev" title="Transformations" href="transformations.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="privileges.html" title="User management"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="transformations.html" title="Transformations"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" accesskey="U">User Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Bookmarks</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="bookmarks">
<span id="id1"></span><h1>Bookmarks<a class="headerlink" href="#bookmarks" title="Permalink to this headline">¶</a></h1>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You need to have configured the <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a> for using bookmarks
feature.</p>
</div>
<div class="section" id="storing-bookmarks">
<h2>Storing bookmarks<a class="headerlink" href="#storing-bookmarks" title="Permalink to this headline">¶</a></h2>
<p>Any query that is executed can be marked as a bookmark on the page
where the results are displayed. You will find a button labeled
<span class="guilabel">Bookmark this query</span> just at the end of the page. As soon as you have
stored a bookmark, that query is linked to the database.
You can now access a bookmark dropdown on each page where the query box appears on for that database.</p>
</div>
<div class="section" id="variables-inside-bookmarks">
<h2>Variables inside bookmarks<a class="headerlink" href="#variables-inside-bookmarks" title="Permalink to this headline">¶</a></h2>
<p>Inside a query, you can also add placeholders for variables.
This is done by inserting into the query SQL comments between <code class="docutils literal notranslate"><span class="pre">/*</span></code> and
<code class="docutils literal notranslate"><span class="pre">*/</span></code>. The special string <code class="docutils literal notranslate"><span class="pre">[VARIABLE{variable-number}]</span></code> is used inside the comments.
Be aware that the whole query minus the SQL comments must be
valid by itself, otherwise you won’t be able to store it as a bookmark.
Also, note that the text ‘VARIABLE’ is case-sensitive.</p>
<p>When you execute the bookmark, everything typed into the <em>Variables</em>
input boxes on the query box page will replace the strings <code class="docutils literal notranslate"><span class="pre">/*[VARIABLE{variable-number}]*/</span></code> in
your stored query.</p>
<p>Also remember, that everything else inside the <code class="docutils literal notranslate"><span class="pre">/*[VARIABLE{variable-number}]*/</span></code> string for
your query will remain the way it is, but will be stripped of the <code class="docutils literal notranslate"><span class="pre">/**/</span></code>
chars. So you can use:</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="cm">/*, [VARIABLE1] AS myname */</span>
</pre></div>
</div>
<p>which will be expanded to</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="p">,</span> <span class="n">VARIABLE1</span> <span class="k">as</span> <span class="n">myname</span>
</pre></div>
</div>
<p>in your query, where VARIABLE1 is the string you entered in the Variable 1 input box.</p>
<p>A more complex example, say you have stored this query:</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span> <span class="k">Name</span><span class="p">,</span> <span class="n">Address</span> <span class="k">FROM</span> <span class="n">addresses</span> <span class="k">WHERE</span> <span class="mi">1</span> <span class="cm">/* AND Name LIKE &#39;%[VARIABLE1]%&#39; */</span>
</pre></div>
</div>
<p>If you wish to enter “phpMyAdmin” as the variable for the stored query, the full
query will be:</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span> <span class="k">Name</span><span class="p">,</span> <span class="n">Address</span> <span class="k">FROM</span> <span class="n">addresses</span> <span class="k">WHERE</span> <span class="mi">1</span> <span class="k">AND</span> <span class="k">Name</span> <span class="k">LIKE</span> <span class="s1">&#39;%phpMyAdmin%&#39;</span>
</pre></div>
</div>
<p><strong>NOTE THE ABSENCE OF SPACES</strong> inside the <code class="docutils literal notranslate"><span class="pre">/**/</span></code> construct. Any spaces
inserted there will be later also inserted as spaces in your query and may lead
to unexpected results especially when using the variable expansion inside of a
“LIKE ‘’” expression.</p>
</div>
<div class="section" id="browsing-a-table-using-a-bookmark">
<h2>Browsing a table using a bookmark<a class="headerlink" href="#browsing-a-table-using-a-bookmark" title="Permalink to this headline">¶</a></h2>
<p>When a bookmark has the same name as the table, it will be used as the query when browsing
this table.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="faq.html#faqbookmark"><span class="std std-ref">6.18 Bookmarks: Where can I store bookmarks? Why can’t I see any bookmarks below the query box? What are these variables for?</span></a>,
<a class="reference internal" href="faq.html#faq6-22"><span class="std std-ref">6.22 Bookmarks: Can I execute a default bookmark automatically when entering Browse mode for a table?</span></a></p>
</div>
</div>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Bookmarks</a><ul>
<li><a class="reference internal" href="#storing-bookmarks">Storing bookmarks</a></li>
<li><a class="reference internal" href="#variables-inside-bookmarks">Variables inside bookmarks</a></li>
<li><a class="reference internal" href="#browsing-a-table-using-a-bookmark">Browsing a table using a bookmark</a></li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="transformations.html"
                        title="previous chapter">Transformations</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="privileges.html"
                        title="next chapter">User management</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/bookmarks.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="privileges.html" title="User management"
             >next</a> |</li>
        <li class="right" >
          <a href="transformations.html" title="Transformations"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" >User Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Bookmarks</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>